import ccxt
import argparse
from datetime import datetime, timedelta
import time
import pandas as pd

def parse_arguments():
    parser = argparse.ArgumentParser(description='下载指定标的的1分钟K线数据')
    parser.add_argument('--exchange', required=True, help='交易所名称 (如: binance, okx)')
    parser.add_argument('--symbol', required=True, help='交易对符号 (如: BTC/USDT)')
    parser.add_argument('--start_date', required=True, help='开始日期 (格式: YYYY-mm-dd)')
    parser.add_argument('--end_date', required=True, help='结束日期 (格式: YYYY-mm-dd)')
    parser.add_argument('--output', default='ohlcv.csv', help='输出文件路径 (默认: ohlcv.csv)')
    return parser.parse_args()

def validate_exchange(exchange_name):
    if not hasattr(ccxt, exchange_name):
        raise ValueError(f"不支持的交易所: {exchange_name}")
    exchange = getattr(ccxt, exchange_name)({
        'httpsProxy': 'http://127.0.0.1:7890',  # 设置代理
    	'enableRateLimit': True,
    })
    if not exchange.has['fetchOHLCV']:
        raise ValueError(f"该交易所不支持获取K线数据")
    return exchange

def date_to_timestamp(date_str, end_of_day=False):
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    if end_of_day:
        dt = dt.replace(hour=23, minute=59, second=59, microsecond=999999)
    return int(dt.timestamp() * 1000)

def fetch_ohlcv_data(exchange, symbol, start_ts, end_ts):
    ohlcv = []
    current_ts = start_ts
    max_retries = 3
    timeframe = '1m'

    while current_ts < end_ts:
        for _ in range(max_retries):
            try:
                data = exchange.fetch_ohlcv(symbol, timeframe, since=current_ts, limit=1000)
                break
            except Exception as e:
                print(f"请求失败: {str(e)}，正在重试...")
                time.sleep(2)
        else:
            raise Exception("超过最大重试次数，获取数据失败")

        if not data:
            break

        # 过滤超过结束时间的数据
        filtered = [d for d in data if d[0] <= end_ts]
        ohlcv.extend(filtered)

        # 检查是否已获取所有数据
        if len(filtered) < len(data) or data[-1][0] >= end_ts:
            break

        current_ts = data[-1][0] + 60000  # 下一分钟的时间戳

    return ohlcv

def main():
    args = parse_arguments()
    
    try:
        # 初始化交易所
        exchange = validate_exchange(args.exchange)
        
        # 转换时间范围
        start_ts = date_to_timestamp(args.start_date)
        end_ts = date_to_timestamp(args.end_date, end_of_day=True)
        
        # 获取数据
        print(f"正在从 {args.exchange} 获取数据...")
        ohlcv_data = fetch_ohlcv_data(exchange, args.symbol, start_ts, end_ts)
        
        if not ohlcv_data:
            print("未找到指定时间范围内的数据")
            return

        # 转换为DataFrame
        df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # 保存文件
        df.to_csv(args.output, index=False)
        print(f"数据已保存至: {args.output}")
        print(f"共下载 {len(df)} 条记录，时间范围: {df['datetime'].min()} ~ {df['datetime'].max()}")

    except Exception as e:
        print(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()