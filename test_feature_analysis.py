"""
测试特征重要性分析工具
"""

import pandas as pd
import numpy as np
from feature_importance_analysis import FeatureImportanceAnalyzer

def test_data_loading():
    """测试数据加载功能"""
    print("🧪 测试数据加载...")
    
    csv_file = "big_data/TQQQ_US_5min.csv"
    analyzer = FeatureImportanceAnalyzer(csv_file)
    
    try:
        analyzer.load_and_prepare_data()
        print(f"✅ 数据加载成功")
        print(f"   - 特征数量: {len(analyzer.feature_names)}")
        print(f"   - 样本数量: {len(analyzer.target)}")
        print(f"   - 目标分布: {analyzer.target.value_counts().to_dict()}")
        return True
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False

def test_quick_analysis():
    """快速测试分析功能"""
    print("\n🧪 测试快速分析...")
    
    csv_file = "big_data/TQQQ_US_5min.csv"
    analyzer = FeatureImportanceAnalyzer(csv_file)
    
    try:
        # 只加载数据和做相关性分析
        analyzer.load_and_prepare_data()
        analyzer.analyze_correlation()
        
        print("✅ 相关性分析成功")
        corr_results = analyzer.results['correlation']
        print("   Top 5 相关性特征:")
        for i, (_, row) in enumerate(corr_results.head(5).iterrows(), 1):
            print(f"   {i}. {row['feature']}: {row['pearson_corr']:.4f}")
            
        return True
    except Exception as e:
        print(f"❌ 快速分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 特征重要性分析工具测试")
    print("=" * 40)
    
    # 测试数据加载
    if not test_data_loading():
        return
        
    # 测试快速分析
    if not test_quick_analysis():
        return
        
    print("\n✅ 所有测试通过！")
    print("\n💡 现在可以运行完整分析:")
    print("   python run_feature_analysis.py")

if __name__ == "__main__":
    main()
