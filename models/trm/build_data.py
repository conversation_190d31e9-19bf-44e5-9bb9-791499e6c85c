# type: ignore
import sys, os
# 把项目根目录添加到系统路径中
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
print(sys.path)
from datetime import datetime
import pytz
import exchange_calendars as ecals
import pandas as pd
from decimal import Decimal
from logs.log import logger
from longport.openapi import Config, QuoteContext, CalcIndex, Period, TradeContext, \
	OrderType, OrderSide, TimeInForceType, AdjustType, OutsideRTH
    
from dotenv import load_dotenv

DIRT_SHORT = 0  # 空头仓位
DIRT_LONG = 1  # 多头仓位

# 加载环境变量
load_dotenv()
# 初始化行情和交易ctx
config = Config.from_env()
trade_ctx = TradeContext(config)
query_ctx = QuoteContext(config)
print(f'当前账户余额: {trade_ctx.account_balance()}')

def get_positions():
    resp = trade_ctx.stock_positions()
    positions = resp.channels[0].positions
    return positions

def get_balance():
    balance_info = trade_ctx.account_balance()
    return float(balance_info[0].cash_infos[0].available_cash)

def get_symbol_price(symbol: str) -> Decimal:
    resp = query_ctx.quote([symbol])
    print(f'get_symbol_price resp:{resp} symbol:{symbol}')
    # 判断 当前时间是夜盘, 盘前, 还是盘后
    if is_us_night_trade_time():
        if resp[0].overnight_quote:
            return resp[0].overnight_quote.last_done
        return resp[0].post_market_quote.last_done
    if is_us_pre_trade_time():
        return resp[0].pre_market_quote.last_done
    if is_us_regular_trade_time():
        return resp[0].last_done
    if is_us_post_trade_time():
        return resp[0].post_market_quote.last_done
    return resp[0].last_done

def get_symbol_fundamentals(symbol: str) -> dict:
    idx_list = [
        CalcIndex.Volume, CalcIndex.TurnoverRate, CalcIndex.CapitalFlow, CalcIndex.Amplitude, 
        CalcIndex.VolumeRatio, CalcIndex.PeTtmRatio, CalcIndex.PbRatio, 
    ]
    resp = query_ctx.calc_indexes([symbol], idx_list)
    return {
        'symbol': symbol,
        'volume': resp[0].volume,
        'turnover_rate': resp[0].turnover_rate,
        'capital_flow': resp[0].capital_flow,
        'amplitude': resp[0].amplitude,
        'volume_ratio': resp[0].volume_ratio,
        'pe_ttm_ratio': resp[0].pe_ttm_ratio,
        'pb_ratio': resp[0].pb_ratio,
    }


def get_kline_data(symbol: str, period: Period, end_dt: datetime, count: int) -> pd.DataFrame:
    resp = query_ctx.history_candlesticks_by_offset(symbol, period,
        AdjustType.ForwardAdjust, forward=False, count=count, time=end_dt)
    if not resp:
        return pd.DataFrame()

    data = [
        {
            "open": c.open,
            "high": c.high,
            "low": c.low,
            "close": c.close,
            "volume": c.volume,
            "turnover": c.turnover,
            "timestamp": c.timestamp,
        }
        for c in resp
    ]
    df = pd.DataFrame(data)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df['open'] = df['open'].astype(float)
    df['high'] = df['high'].astype(float)
    df['low'] = df['low'].astype(float)
    df['close'] = df['close'].astype(float)
    df['volume'] = df['volume'].astype(float)
    df['turnover'] = df['turnover'].astype(float)
    df.set_index('timestamp', inplace=True)
    return df


def get_kline_data_batch(symbol: str, period: Period, start_dt: datetime, end_dt: datetime, batch_size: int = 1000) -> pd.DataFrame:
    """
    批量获取K线数据，突破单次1000条的限制

    Args:
        symbol: 股票代码，如 'TQQQ.US'
        period: 时间周期，如 Period.Min_5
        start_dt: 开始时间
        end_dt: 结束时间
        batch_size: 每批获取的数量，默认1000

    Returns:
        合并后的完整DataFrame
    """
    all_data = []
    current_end = end_dt

    logger.info(f"开始批量获取 {symbol} 的K线数据，从 {start_dt} 到 {end_dt}")

    batch_count = 0
    while True:
        batch_count += 1
        logger.info(f"正在获取第 {batch_count} 批数据，截止时间: {current_end}")

        # 获取当前批次的数据
        batch_df = get_kline_data(symbol, period, current_end, batch_size)

        if batch_df.empty:
            logger.warning(f"第 {batch_count} 批数据为空，停止获取")
            break

        # 过滤出在指定时间范围内的数据
        batch_df = batch_df[batch_df.index >= start_dt]

        if batch_df.empty:
            logger.info(f"第 {batch_count} 批数据已超出起始时间范围，停止获取")
            break

        all_data.append(batch_df)
        logger.info(f"第 {batch_count} 批获取了 {len(batch_df)} 条数据")

        # 更新下一批的结束时间为当前批次最早的时间
        current_end = batch_df.index.min()

        # 如果当前批次的最早时间已经达到或超过起始时间，停止获取
        if current_end <= start_dt:
            logger.info(f"已获取到起始时间 {start_dt}，停止获取")
            break

        # 如果当前批次数据量小于batch_size，说明已经获取完所有数据
        if len(batch_df) < batch_size:
            logger.info(f"当前批次数据量 {len(batch_df)} 小于批次大小 {batch_size}，已获取完所有数据")
            break

    if not all_data:
        logger.warning("未获取到任何数据")
        return pd.DataFrame()

    # 合并所有数据
    combined_df = pd.concat(all_data, axis=0)

    # 去重并排序
    combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
    combined_df = combined_df.sort_index()

    # 再次过滤确保在时间范围内
    combined_df = combined_df[(combined_df.index >= start_dt) & (combined_df.index <= end_dt)]

    logger.info(f"批量获取完成！总共获取了 {len(combined_df)} 条数据")
    return combined_df

def download_kline_batch(symbol: str, period: Period, start_dt: datetime, end_dt: datetime) -> pd.DataFrame:
    print(f"开始获取 {symbol} 从 {start_time} 到 {end_time} 的5分钟K线数据...")

    # 使用批量获取函数，可以获取超过1000条的数据
    df_batch = get_kline_data_batch(symbol, period, start_time, end_time)
    print(f"批量获取完成！共获取 {len(df_batch)} 条数据")
    print(df_batch.head())
    print("...")
    print(df_batch.tail())

    # 保存到CSV文件
    if not df_batch.empty:
        filename = f"{symbol.replace('.', '_')}_{start_time.strftime('%Y%m%d')}_{end_time.strftime('%Y%m%d')}_5min.csv"
        df_batch.to_csv(f"big_data/{filename}")
        print(f"数据已保存到: big_data/{filename}")

    # 对比：单次获取只能获取1000条
    print("\n对比：单次获取最多1000条数据")
    df_single = get_kline_data(symbol, Period.Min_5, end_time, 1000)
    print(f"单次获取了 {len(df_single)} 条数据")
    print(f"时间范围: {df_single.index.min()} 到 {df_single.index.max()}")


def submit_order(symbol: str, submitted_price=None, order_type=OrderType.MO, order_side=OrderSide.Buy, quantity=1, time_in_force_type=TimeInForceType.Day, dry_run=True) -> str:
    outside_rth = OutsideRTH.Overnight if is_us_night_trade_time() else OutsideRTH.AnyTime
    logger.info(f'下单: {symbol}, 订单类型: {order_type}, 订单方向: {order_side}, 数量: {quantity}, 时间: {time_in_force_type}')
    if dry_run:
        return "dry_run"
    if not is_trade_day_time():
        logger.error("非交易时间, 下单失败")
        return ""
    try:
        resp = trade_ctx.submit_order(
            symbol,
            order_type,
            order_side,
            Decimal(quantity),
            submitted_price=None if order_type == OrderType.MO else submitted_price,
            outside_rth=outside_rth,
            time_in_force=time_in_force_type,
        )
        return resp.order_id
    except Exception as e:
        logger.info(f'下单异常: {e}')
        return ""

def submit_stop_loss_order(symbol: str, order_side=OrderSide.Sell, quantity=1, dry_run=True) -> str:
    outside_rth = OutsideRTH.Overnight if is_us_night_trade_time() else OutsideRTH.AnyTime
    if dry_run:
        return "dry_run"
    try:
        resp = trade_ctx.submit_order(
            symbol,
            OrderType.TSLPPCT,
            order_side,
            Decimal(quantity),
            TimeInForceType.GoodTilCanceled,
            trailing_percent=Decimal("0.5"),
            outside_rth=outside_rth,
        )
        return resp.order_id
    except:
        return ""



def is_trade_day_time():
    '''
    不管是夏令时还是冬令时, 时间都还是一样, 只不过是自动从夏令时的UTC-4变到了冬令时的UTC-5
    盘前: 4:00AM - 9:30AM
    盘中: 9:30AM - 4:00PM
    盘后: 4:00PM - 8:00PM
    夜盘: 8:00PM - 4:00AM
    '''
    eastern = pytz.timezone("America/New_York")
    now = datetime.now(eastern)
    now_str = now.strftime("%Y-%m-%d")
    nyse = ecals.get_calendar("XNYS")
    # 检查某日是否开市
    if not nyse.is_session(now_str):  # 是节日直接返回 False
        return False
    '''
    工作日每天美东4:00 - 20:00，剩下8小时交易所闭市了, 但可以在长桥券商内部玩夜盘, 所以一直能玩到周六的4:00AM
    然后美东周日20:00 - 周一4:00就已经可以夜盘了
    '''
    # 获取当前星期几 (周一=0, 周日=6)
    weekday = now.weekday()
    # 获取当前时间的小时和分钟
    current_time = now.time()
    # 检查是否是周六(5) 4:00之后
    if weekday == 5 and current_time >= datetime.strptime("04:00", "%H:%M").time():
        return False
    # 检查是否是周日(6) 20:00之前
    elif weekday == 6 and current_time < datetime.strptime("20:00", "%H:%M").time():
        return False
    return True

def is_us_pre_trade_time():
    eastern = pytz.timezone("America/New_York")
    now = datetime.now(eastern)
    current_time = now.hour + now.minute/60  # 转换为小时小数表示
    return 4 <= current_time < 9.5  # 4:00-9:30

def is_us_regular_trade_time():
    eastern = pytz.timezone("America/New_York")
    now = datetime.now(eastern)
    current_time = now.hour + now.minute/60  # 转换为小时小数表示
    return 9.5 <= current_time < 16  # 9:30-16:00

def is_us_post_trade_time():
    eastern = pytz.timezone("America/New_York")
    now = datetime.now(eastern)
    current_time = now.hour + now.minute/60  # 转换为小时小数表示
    return 16 <= current_time < 20  # 16:00-20:00

def is_us_night_trade_time():
    eastern = pytz.timezone("America/New_York")
    now = datetime.now(eastern)
    current_time = now.hour + now.minute/60  # 转换为小时小数表示
    return 20 <= current_time or current_time < 4  # 20:00-4:00


def have_position(symbol: str, direction: int):
    positions = get_positions()
    for position in positions:
        if position.symbol != symbol:
            continue
        if position.available_quantity > 0 and direction == DIRT_LONG:
            return True
        if position.available_quantity < 0 and direction == DIRT_SHORT:
            return True
    return False

def get_position_quantity(symbol: str, direction: int):
    positions = get_positions()
    for position in positions:
        if position.symbol != symbol:
            continue
        if position.available_quantity > 0 and direction == DIRT_LONG:
            return position.available_quantity
        if position.available_quantity < 0 and direction == DIRT_SHORT:
            return -position.available_quantity
    return 0


def close_position(symbol, direction: int, quantity, dry_run=True):
    quantity = get_position_quantity(symbol, direction)
    if quantity == 0:
        return False
    # 先平多
    if direction == DIRT_LONG:
        submit_order(
            symbol=symbol,
            order_type=OrderType.MO,
            order_side=OrderSide.Sell,
            quantity=quantity,
            dry_run=dry_run,
        )
    # 先平空
    if direction == DIRT_SHORT:
        submit_order(
            symbol=symbol,
            order_type=OrderType.MO,
            order_side=OrderSide.Buy,
            quantity=quantity,
            dry_run=dry_run,
        )

def close_exist_positions(symbol: str, direction: int, dry_run=True):
    if symbol in ['UGL', 'GLL', 'YINN', 'YANG', 'TQQQ', 'SQQQ']:
        if symbol == 'UGL':
            quantity = get_position_quantity('GLL', DIRT_LONG)
            if quantity:
                close_position(symbol, DIRT_LONG, quantity, dry_run=dry_run)
        elif symbol == 'GLL':
            quantity = get_position_quantity('UGL', DIRT_LONG)
            if quantity:
                close_position(symbol, DIRT_LONG, quantity, dry_run=dry_run)
        elif symbol == 'YINN':
            quantity = get_position_quantity('YANG', DIRT_LONG)
            if quantity:
                close_position(symbol, DIRT_LONG, quantity, dry_run=dry_run)
        elif symbol == 'YANG':
            quantity = get_position_quantity('YINN', DIRT_LONG)
            if quantity:
                close_position(symbol, DIRT_LONG, quantity, dry_run=dry_run)
        elif symbol == 'TQQQ':
            quantity = get_position_quantity('SQQQ', DIRT_LONG)
            if quantity:
                close_position(symbol, DIRT_LONG, quantity, dry_run=dry_run)
        elif symbol == 'SQQQ':
            quantity = get_position_quantity('TQQQ', DIRT_LONG)
            if quantity:
                close_position(symbol, DIRT_LONG, quantity, dry_run=dry_run)
    else:
        quantity = get_position_quantity(symbol, direction)
        reverse_dirt = 1 if direction == 0 else 0
        close_position(symbol, reverse_dirt, quantity, dry_run=dry_run)


def do_long(news_id: int, symbol: str, total_amount: int, title: str, action: str, content: str, dry_run=True):
    # 有该标的的空头仓位, 先平空
    close_exist_positions(symbol, DIRT_SHORT, dry_run=dry_run)
    # 判断是否有该标的的多头仓位
    if have_position(symbol, DIRT_LONG):
        logger.info(f'已经有 {symbol} 的多头仓位，不下单')
        return False
    price = get_symbol_price(symbol)
    if not price:
        logger.error(f'获取 {symbol} 价格失败')
        return False
    # 判断余额是否足够下单
    balance = get_balance()
    if balance < total_amount:
        logger.error(f'余额不足，下单失败')
        return False
    # 计算下单数量
    quantity = total_amount // float(price)
    news = UsNews.select().where(UsNews.news_id == news_id).first()
    if news:
        news.open_price = price
        news.stock_nums = quantity
        news.direction = DIRT_LONG
        news.save()
    # 开多仓
    order_id = submit_order(
        symbol=symbol,
        order_type=OrderType.MO,
        order_side=OrderSide.Buy,
        quantity=quantity,
        dry_run=dry_run,
    )
    if order_id:  # 下一个跟踪止盈单
        submit_stop_loss_order(symbol, OrderSide.Sell, quantity, dry_run=dry_run)
    else:
        logger.error(f'开多仓失败')
    return True
    

def do_short(news_id: int, symbol: str, total_amount: int, title: str, action: str, content: str, dry_run=True):
    # 有该标的的多头仓位, 先平仓
    close_exist_positions(symbol, DIRT_LONG, dry_run=dry_run)
    # 判断是否有该标的的空头仓位
    if have_position(symbol, DIRT_SHORT):
        logger.info(f'已经有 {symbol} 的空头仓位，不下单')
        return False
    price = get_symbol_price(symbol)
    if not price:
        logger.error(f'获取 {symbol} 价格失败')
        return False
    # 判断余额是否足够下单
    balance = get_balance()
    if balance < total_amount:
        logger.error(f'余额不足，下单失败')
        return False
    # 计算下单数量
    quantity = total_amount // float(price)
    news = UsNews.select().where(UsNews.news_id == news_id).first()
    if news:
        news.open_price = price
        news.stock_nums = quantity
        news.direction = DIRT_SHORT
        news.save()
    # 开空仓
    order_id = submit_order(
        symbol=symbol,
        order_type=OrderType.MO,
        order_side=OrderSide.Sell,
        quantity=quantity,
        dry_run=dry_run,
    )
    if order_id:  # 下一个跟踪止盈单
        submit_stop_loss_order(symbol, OrderSide.Buy, quantity, dry_run=dry_run)
    else:
        logger.error(f'开空仓失败')
    return True


if __name__ == '__main__':
    # 示例：批量获取大量历史数据
    symbol = 'TQQQ.US' # 注意这里下的时间都是中国的时间, 只有盘中数据
    # start_time = datetime(2023, 12, 4)
    # end_time = datetime(2025, 1, 1)
    start_time = datetime(2025, 1, 1)
    end_time = datetime(2025, 7, 1)

    download_kline_batch(symbol, Period.Min_5, start_time, end_time)

    # 其他示例代码（注释掉）
    # resp = submit_order('UGL.US', Decimal("3.5"), OrderType.LO, OrderSide.Buy, 1, TimeInForceType.Day, dry_run=True)
    # resp = get_symbol_fundamentals('NVDA.US')
    # resp = query_ctx.quote(['UGL.US'])
