{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["这是第五版\n", "\n", "改进了\n", "- DataLoader分批次训练\n", "- BatchNorm\n", "- 训练NaN\n", "\n", "仍然存在:\n", "- 损失太大, 降不下来\n", "- 预测值 太小, 实际值大"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "from torch import nn\n", "from torch.utils.data import TensorDataset, DataLoader\n", "import torch\n", "from torch.nn.utils.parametrizations import weight_norm\n", "\n", "class Chomp1d(nn.<PERSON><PERSON><PERSON>):  \n", "\t# 裁剪多余填充的未来数据以保持因果性\n", "\tdef __init__(self, chomp_size):\n", "\t\tsuper().__init__()\n", "\t\tself.chomp_size = chomp_size\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\treturn x[:, :, :-self.chomp_size].contiguous()\n", "\t\n", "class Permute(nn.Module):\n", "    def __init__(self, dims):\n", "        super().__init__()\n", "        self.dims = dims\n", "    def forward(self, x):\n", "        return x.permute(*self.dims)\n", "\n", "class TemporalBlock(nn.Module):\n", "\t\n", "\tdef __init__(self, n_inputs, n_outputs, kernel_size, dilation):\n", "\t\t'''\n", "\t\t- n_inputs: 输入通道数\n", "\t\t- n_outputs: 输出通道数\n", "\t\t- kernel_size: 卷积核大小\n", "\t\t- dilation: 膨胀系数\n", "\t\t'''\n", "\t\tsuper().__init__()\n", "\t\tpadding = (kernel_size-1) * dilation  # 因果卷积的填充计算\n", "\t\tself.conv = nn.Sequential(\n", "\t\t\t# weight_norm对权重矩阵做归一化\n", "\t\t\tweight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),\n", "\t\t\tChomp1d(padding),\n", "\t\t\tPermute((0, 2, 1)),\n", "\t\t\tnn.LayerNorm(n_outputs),  # 对每个样本的通道维度归一化\n", "\t\t\tPermute((0, 2, 1)),\n", "\t\t\tnn.ReLU(),\n", "\t\t\tnn.Dropout(0.4)\n", "\t\t)\n", "\t\tself.conv2 = nn.Sequential(\n", "\t\t\tweight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),\n", "\t\t\tChomp1d(padding),\n", "\t\t\tPermute((0, 2, 1)),\n", "\t\t\tnn.LayerNorm(n_outputs),\n", "\t\t\tPermute((0, 2, 1)),\n", "\t\t\tnn.ReLU(),\n", "\t\t\tnn.Dropout(0.4)\n", "\t\t)\n", "\t\tself.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\tout = self.conv(x)\n", "\t\tout = self.conv2(out)\n", "\t\tres = x if self.downsample is None else self.downsample(x)\n", "\t\treturn nn.ReLU()(out + res)  # 残差连接\n", "\t\n", "\n", "class TCN(nn.Module):\n", "\t'''\n", "\t- input_dim: 输入通道数\n", "\t- num_channels: 共几层, 每层的输出通道数\n", "\t- kernel_size: 卷积核大小\n", "\t- output_size: 输出通道数\n", "\t'''\n", "\tdef __init__(self, input_dim, num_channels, kernel_size=3, output_size=1):\n", "\t\tsuper().__init__()\n", "\t\tlayers = []\n", "\t\tnum_levels = len(num_channels)\n", "\t\tfor i in range(num_levels):\n", "\t\t\tdilation = 2 ** i  # 指数级膨胀系数\n", "\t\t\tlayers += [TemporalBlock(input_dim if i==0 else num_channels[i-1], \n", "\t\t\t\t\t\t\t\t\tnum_channels[i], kernel_size, dilation)]\n", "\t\tself.network = nn.Sequential(*layers)\n", "\t\tself.linear = nn.Linear(num_channels[-1], output_size)\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\t# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)\n", "\t\tx = x.permute(0, 2, 1)\n", "\t\tx = self.network(x)\n", "\t\tx = x[:, :, -1]  # 取序列的最后一个时间步 (200,32,30) -> (200,32)\n", "\t\tx = self.linear(x)  # 将输出传递给线性层 (200,32) -> (200,1)\n", "\t\treturn x.squeeze()  # (200,1) -> (200)\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using device: {device}\")\n", "\n", "# 读取数据\n", "df = pd.read_csv('./sample_data/XRP_USDT.csv')\n", "\n", "# 计算收益率\n", "data = pd.DataFrame()\n", "data['Return'] = np.log(df['close'] / df['close'].shift(1))\n", "# 计算成交量\n", "data['LogVolume'] = np.log(df['volume'] + 1)\n", "# 计算买卖方实力\n", "data['BuyPower'] = df['buy_volume'] / (df['volume'] + 1e-8) - 0.5\n", "# 计算IBS\n", "data['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)\n", "\n", "\n", "# 预计几个tick后的收益率\n", "AFTER_TICK_NUM = 15\n", "# 准备 特征和标签\n", "data.replace([np.inf, -np.inf], np.nan, inplace=True)\n", "data = data.dropna()\n", "features = data[['Return', 'LogVolume', 'BuyPower', 'IBS']].values\n", "labels = data['Return'].shift(-AFTER_TICK_NUM).values  # 预测几天后的收益率\n", "labels = labels[:-AFTER_TICK_NUM]  # 删除最后几个NaN\n", "features = features[:-AFTER_TICK_NUM]  # 确保 特征 和 标签 的长度相同\n", "print(f'labels len:{len(labels)}, features len:{len(features)}.')\n", "print(np.any(np.isnan(features)), np.any(np.isinf(features)))\n", "print(np.any(np.isnan(labels)), np.any(np.isinf(labels)))\n", "\n", "# 将特征数据转换为时间序列, 每个样本包含30个时间步的特征\n", "SEQ_LEN = 30\n", "X, y = [], []\n", "max_index = len(features) - SEQ_LEN\n", "for i in range(max_index):\n", "\tX.append(features[i:i + SEQ_LEN])\n", "\ty.append(labels[i + SEQ_LEN - 1])  # 取训练数据最后一个时间步的标签作为预测目标\n", "X, y = np.array(X), np.array(y)\n", "print(f'X shape: {X.shape}, y shape: {y.shape}')\n", "\n", "# 将数据划分为 训练集 和 测试集\n", "split_idx = int(len(X) - 256)  # 保留最后几个样本作为测试集\n", "X_train_val, X_test = X[:split_idx], X[split_idx:]\n", "y_train_val, y_test = y[:split_idx], y[split_idx:]\n", "\n", "# 训练集 进一步划分 训练集 和 验证集\n", "val_size = int(0.2 * len(X_train_val))  # 20%作为验证集\n", "X_train, X_val = X_train_val[:-val_size], X_train_val[-val_size:]\n", "y_train, y_val = y_train_val[:-val_size], y_train_val[-val_size:]\n", "print(f'X_train shape: {X_train.shape}, X_val shape: {X_val.shape}, X_test shape: {X_test.shape}')\n", "print(f'y_train shape: {y_train.shape}, y_val shape: {y_val.shape}, y_test shape: {y_test.shape}')\n", "\n", "\n", "# 创建数据集\n", "train_dataset = TensorDataset(\n", "\ttorch.from_numpy(X_train).float(),\n", "\ttorch.from_numpy(y_train).float()\n", ")\n", "val_dataset = TensorDataset(\n", "\ttorch.from_numpy(X_val).float(),\n", "\ttorch.from_numpy(y_val).float()\n", ")\n", "test_dataset = TensorDataset(\n", "\ttorch.from_numpy(X_test).float(),\n", "\ttorch.from_numpy(y_test).float()\n", ")\n", "\n", "# 创建DataLoader\n", "batch_size = 32  # 一次训练多少样本\n", "train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)\n", "val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)\n", "\n", "# 定义模型 优化器 学习器\n", "model = TCN(features.shape[-1], (32, 32, 32, 32)).to(device)\n", "# 学习率lr控制参数更新的步长, ​权重衰减weight_decay抑制参数值过大\n", "optimizer = torch.optim.Adam(model.parameters(), lr=1e-5, weight_decay=1e-4)\n", "scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(\n", "\toptimizer, \n", "\tmode='min',\n", "\tfactor=0.5,\n", "\tpatience=3\n", ")\n", "loss_fn = nn.L1Loss()\n", "model_path = 'model_tcnV5.pth'\n", "\n", "def train():\n", "\t# 早停机制\n", "\tbest_loss = float('inf')\n", "\tpatience = 10\n", "\tno_improve = 0\n", "\tfor epoch in range(1000):\n", "\t\t# --------------------- 训练 ---------------------\n", "\t\tmodel.train()  # 切换到训练模式, 启用 Batch Normalization 和 Dropout\n", "\t\ttrain_loss = 0.0\n", "\t\tfor x_batch, y_batch in train_loader:\n", "\t\t\tx_batch, y_batch = x_batch.to(device), y_batch.to(device)\n", "\t\t\toptimizer.zero_grad()  # 梯度清零\n", "\t\t\ty_pred = model(x_batch)  # 前向传播\n", "\t\t\tloss = loss_fn(y_pred, y_batch)  # 计算loss\n", "\t\t\tloss.backward()  # 反向传播\n", "\t\t\ttorch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)\n", "\t\t\toptimizer.step()  # 更新参数\n", "\t\t\ttrain_loss += (loss.item() * x_batch.size(0))\n", "\t\ttrain_loss /= len(train_loader.dataset)  # 计算平均训练损失\n", "\n", "\t\t# --------------------- 验证 ---------------------\n", "\t\tmodel.eval()  # 切换到验证模式, 禁用 Batch Normalization 和 Dropout\n", "\t\tval_loss = 0.0\n", "\t\twith torch.no_grad():\n", "\t\t\tfor x_val, y_val in val_loader:\n", "\t\t\t\tx_val, y_val = x_val.to(device), y_val.to(device)\n", "\t\t\t\ty_pred_val = model(x_val)\n", "\t\t\t\tval_loss += loss_fn(y_pred_val, y_val).item() * x_val.size(0)\n", "\t\tval_loss /= len(val_loader.dataset)  # 计算平均验证损失\n", "\t\tscheduler.step(val_loss)  # 更新学习率\n", "\t\t\n", "\t\tprint(f'Epoch {epoch}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')\n", "\n", "\t\t# --------------------- 早停 ---------------------\n", "\t\tif val_loss < best_loss:\n", "\t\t\tbest_loss = val_loss\n", "\t\t\tprint('保存')\n", "\t\t\ttorch.save(model.state_dict(), model_path)\n", "\t\t\tno_improve = 0\n", "\t\telse:\n", "\t\t\tno_improve += 1\n", "\t\t\tif no_improve >= patience:\n", "\t\t\t\tprint(f'Early stopping at epoch {epoch}')\n", "\t\t\t\tbreak\n", "\tprint('训练完成')\n", "\n", "# 加载模型\n", "if os.path.exists(model_path):\n", "\tprint('正在加载模型...')\n", "\tmodel.load_state_dict(torch.load(model_path, weights_only=True))\n", "\tprint('加载模型成功')\n", "else:\n", "\tprint('开始从零训练模型...')\n", "\ttrain()\n", "\n", "# --------------------- 测试 ---------------------\n", "model.eval()  # 切换到验证模式, 禁用 Batch Normalization 和 Dropout\n", "\n", "# 创建存储窗口\n", "test_preds = []\n", "test_true = []\n", "test_loss = 0.0\n", "with torch.no_grad():\n", "\tfor x_test, y_test in test_loader:\n", "\t\tx_test, y_test = x_test.to(device), y_test.to(device)\n", "\t\ty_pred_test = model(x_test)\n", "\t\ttest_preds.append(y_pred_test.cpu().numpy())\n", "\t\ttest_true.append(y_test.cpu().numpy())\n", "\t\ttest_loss += loss_fn(y_pred_test, y_test).item() * x_test.size(0)\n", "test_loss /= len(test_loader.dataset)\n", "print(f'Test Loss: {test_loss:.6f}')\n", "\n", "# --------------------- 画图 ---------------------\n", "test_preds = np.concatenate(test_preds)\n", "test_true = np.concatenate(test_true)\n", "print(f'test_preds shape: {test_preds.shape}, test_true shape: {test_true.shape}')\n", "# 创建时间轴（使用测试集样本数）\n", "time_steps = np.arange(len(test_true))\n", "# 创建画布\n", "plt.figure(figsize=(14, 6))\n", "# 绘制实际值和预测值曲线\n", "plt.plot(time_steps, test_preds, label='Predicted Returns', alpha=0.7)\n", "plt.plot(time_steps, test_true, label='Actual Returns', alpha=0.7)\n", "# 添加统计指标\n", "mse = ((test_true - test_preds) ** 2).mean()\n", "corr = np.corrcoef(test_true, test_preds)[0, 1]\n", "plt.title(f\"Return Prediction (MSE: {mse:.4f}, Corr: {corr:.4f})\")\n", "plt.xlabel(\"Time Steps\")\n", "plt.ylabel(\"Log Returns\")\n", "plt.legend()\n", "plt.show()\n", "print('绘制完成')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}