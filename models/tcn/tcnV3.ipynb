{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["这是第三版, 有几个缺陷\n", "- 没加transformer\n", "\n", "改进了\n", "- K线数据不够, 没加主买量, ohl\n", "- 训练数据不够多(下多点分钟级数据)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.model_selection import train_test_split\n", "import ccxt"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["服务器时间: 1741413315029\n"]}], "source": ["# Step 1: Fetch data\n", "exchange = ccxt.binance({\n", "    'httpsProxy': 'http://172.22.48.1:7890',  # 设置代理\n", "    'enableRateLimit': True,  # 启用速率限制\n", "})\n", "symbol = 'SOL/USDT'  # 交易对\n", "timeframe = '1m'     # 时间框架（1分钟）\n", "since = exchange.parse8601('2024-03-04T00:00:00Z')  # 开始时间（ISO 8601格式）\n", "\n", "# 获取 Binance 服务器时间\n", "server_time = exchange.fetch_time()\n", "print(f\"服务器时间: {server_time}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 下载数据\n", "ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since)\n", "\n", "# 转换为DataFrame\n", "df = pd.DataFrame(ohlcv, columns=['date', 'open', 'high', 'low', 'close', 'volume'])\n", "# 将时间戳转换为日期格式\n", "data = pd.DataFrame()\n", "\n", "# 预计几天后的收益率\n", "AFTER_TICK_NUM = 5\n", "# 计算收益率\n", "data['Return'] = np.log(df['close'] / df['close'].shift(1))\n", "# 计算成交量\n", "data['LogVolume'] = np.log(df['volume'] + 1)\n", "data = data.dropna()\n", "# 计算买卖方实力\n", "data['BuyPower'] = df['buy_volume'] / df['volume'] - 0.5\n", "# 计算IBS\n", "data['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'])\n", "# 准备 特征和标签\n", "features = data[['Return', 'LogVolume', 'BuyPower', 'IBS']].values\n", "labels = data['Return'].shift(-AFTER_TICK_NUM).dropna().values  # 预测10天后的收益率"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 确保 特征 和 标签 的长度相同\n", "AFTER_TICK_NUM = 10\n", "features = features[:-AFTER_TICK_NUM]\n", "# 对特征进行标准化\n", "scaler = StandardScaler()\n", "features = scaler.fit_transform(features)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4\n"]}], "source": ["print(features.shape[-1])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 将特征数据转换为时间序列, 每个样本包含30个时间步的特征\n", "SQL_LEN = 30\n", "X, y = [], []\n", "for i in range(len(features) - SQL_LEN):\n", "    X.append(features[i:i + SQL_LEN])\n", "    y.append(labels[i + SQL_LEN - 1])  # 取训练数据最后一个时间步的标签作为预测目标\n", "X, y = np.array(X), np.array(y)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 将数据划分为 训练集 和 测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n"]}], "source": ["from torch import nn\n", "from torch.nn.utils.parametrizations import weight_norm\n", "import torch\n", "\n", "class Chomp1d(nn.<PERSON><PERSON><PERSON>):  \n", "\t# 裁剪多余填充的未来数据以保持因果性\n", "\tdef __init__(self, chomp_size):\n", "\t\tsuper().__init__()\n", "\t\tself.chomp_size = chomp_size\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\treturn x[:, :, :-self.chomp_size].contiguous()\n", "\t\n", "\n", "class TemporalBlock(nn.Module):\n", "\t\n", "\tdef __init__(self, n_inputs, n_outputs, kernel_size, dilation):\n", "\t\t'''\n", "\t\t- n_inputs: 输入通道数\n", "\t\t- n_outputs: 输出通道数\n", "\t\t- kernel_size: 卷积核大小\n", "\t\t- dilation: 膨胀系数\n", "\t\t'''\n", "\t\tsuper().__init__()\n", "\t\tpadding = (kernel_size-1) * dilation  # 因果卷积的填充计算\n", "\t\tself.conv = nn.Sequential(\n", "\t\t\t# nn.Conv1d的padding是左右各填充padding个\n", "\t\t\tweight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),\n", "\t\t\tChomp1d(padding),\n", "\t\t\tnn.ReLU(),\n", "\t\t\tnn.Dropout(0.2)\n", "\t\t)\n", "\t\tself.conv2 = nn.Sequential(\n", "\t\t\tweight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),\n", "\t\t\tChomp1d(padding),\n", "\t\t\tnn.ReLU(),\n", "\t\t\tnn.Dropout(0.2)\n", "\t\t)\n", "\t\tself.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\t# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)\n", "\t\tout = self.conv(x)\n", "\t\tout = self.conv2(x)\n", "\t\tres = x if self.downsample is None else self.downsample(x)\n", "\t\treturn nn.ReLU()(out + res)  # 残差连接\n", "\t\n", "\n", "class TCN(nn.Module):\n", "\t'''\n", "\t- input_dim: 输入通道数\n", "\t- num_channels: 共几层, 每层的输出通道数\n", "\t- kernel_size: 卷积核大小\n", "\t- output_size: 输出通道数\n", "\t'''\n", "\tdef __init__(self, input_dim, num_channels, kernel_size=3, output_size=1):\n", "\t\tsuper().__init__()\n", "\t\tlayers = []\n", "\t\tnum_levels = len(num_channels)\n", "\t\tfor i in range(num_levels):\n", "\t\t\tdilation = 2 ** i  # 指数级膨胀系数\n", "\t\t\tprint(i)\n", "\t\t\tlayers += [TemporalBlock(input_dim if i==0 else num_channels[i-1], \n", "\t\t\t\t\t\t\t\t\tnum_channels[i], kernel_size, dilation)]\n", "\t\tself.network = nn.Sequential(*layers)\n", "\t\tself.linear = nn.Linear(num_channels[-1], output_size)\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\t# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)\n", "\t\tx = x.permute(0, 2, 1)\n", "\t\tx = self.network(x)\n", "\t\tx = x[:, :, -1]  # 取序列的最后一个时间步 (200,32,30) -> (200,32)\n", "\t\tx = self.linear(x)  # 将输出传递给线性层 (200,32) -> (200,1)\n", "\t\treturn x.squeeze()  # (200,1) -> (200)\n", "\t\n", "tcn = TCN(features.shape[-1], (32, 64, 32))  # 实例化模型\n", "optimizer = torch.optim.Adam(tcn.parameters())\n", "loss_fn = nn.MSELoss()\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练到第0次, loss:0.12940067052841187\n", "训练到第100次, loss:0.0016706573078408837\n", "训练到第200次, loss:0.0010715887183323503\n", "训练到第300次, loss:0.0008196976268664002\n", "训练到第400次, loss:0.0006472727982327342\n", "训练到第500次, loss:0.00041674141539260745\n", "训练到第600次, loss:0.0003493550175335258\n", "训练到第700次, loss:0.00039100454887375236\n", "训练到第800次, loss:0.0002925891021732241\n", "训练到第900次, loss:0.0002740542986430228\n", "训练完成\n"]}], "source": ["# 训练模型\n", "epochs = 1000\n", "x = torch.from_numpy(X_train).float()\n", "y = torch.from_numpy(y_train).float()\n", "for epoch in range(epochs):\n", "\t# 前向传播\n", "\ty_pred = tcn(x)\n", "\t# 计算loss\n", "\tloss = loss_fn(y_pred, y)\n", "\tif epoch % 100 == 0:\n", "\t\tprint(f'训练到第{epoch}次, loss:{loss}')\n", "\t# 梯度清零\n", "\toptimizer.zero_grad()\n", "\t# 反向传播\n", "\tloss.backward()\n", "\t# 更新参数\n", "\toptimizer.step()\n", "print('训练完成')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'torch.Tensor'>\n", "<class 'torch.Tensor'>\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA08AAAIQCAYAAABZkVWMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzsnXeYG9XVxt9Rl3al7cVl7V13G4wBmx4wBoON6S1AQrDpAUwvCXwJ2CTgkACB0CH0FjoJpmMwYIrBgAGDbdzbrrd4q6RdtZnvjzt3NOqjLtnn9zz7SCuNRqN255573vMeQZIkCQRBEARBEARBEERcdPk+AIIgCIIgCIIgiGKAgieCIAiCIAiCIAgNUPBEEARBEARBEAShAQqeCIIgCIIgCIIgNEDBE0EQBEEQBEEQhAYoeCIIgiAIgiAIgtAABU8EQRAEQRAEQRAaoOCJIAiCIAiCIAhCAxQ8EQRBEARBEARBaICCJ4IgiAwgCALmzZuX78NIm6effhrjxo2D0WhEeXl5vg8nK2zcuBGCIOCJJ57I96HkncbGRsyZM0f5f/HixRAEAYsXL87bMYUTfowEQRD5hIIngiAywrp163DhhRdixIgRsFgscDgcOOigg3D33Xejv78/34dHaGDVqlWYM2cORo4ciUceeQQPP/ywpsddd911EAQBp512WsrP/fPPP2PevHnYuHFjyvsoNp544gkIgqD8WSwWjBkzBnPnzkVra2u+Dy8p3nrrrbwvHqjfS0EQ4HA4MHXqVLz55psp7/PWW2/F66+/nrmDJAii6DHk+wAIgih+3nzzTZx66qkwm80466yzsPvuu8Pr9WLJkiW49tpr8dNPP2meiBcr/f39MBiKe0hdvHgxRFHE3XffjVGjRml6jCRJeP7559HY2Ig33ngDfX19sNvtST/3zz//jPnz5+PQQw9FY2Nj0o8vZm6++WY0NTVhYGAAS5YswQMPPIC33noLK1asgM1my+mxHHLIIejv74fJZErqcW+99Rbuu+++vAdQRxxxBM466yxIkoRNmzbhgQcewLHHHou3334bM2bMSHp/t956K0455RSccMIJmT9YgiCKkuI+0xMEkXc2bNiA008/HcOHD8eHH36IQYMGKfddcsklWLt2bVorv4WMKIrwer2wWCywWCz5Ppy0aWtrA4Ck5HqLFy/G1q1b8eGHH2LGjBl49dVXMXv27Cwd4c7JUUcdhSlTpgAAzjvvPFRVVeHOO+/Ef//7X5xxxhlRH+NyuVBSUpLxY9HpdEX9XR4zZgzOPPNM5f+TTz4ZEyZMwN13351S8JQN1OMGQRDFB8n2CIJIi7///e9wOp149NFHQwInzqhRo3D55Zcr//v9fvzlL3/ByJEjYTab0djYiBtuuAEejyfkcY2NjTjmmGOwePFiTJkyBVarFRMnTlRqMV599VVMnDgRFosFkydPxnfffRfy+Dlz5qC0tBTr16/HjBkzUFJSgsGDB+Pmm2+GJEkh295+++048MADUVVVBavVismTJ+Pll1+OeC2CIGDu3Ll49tlnsdtuu8FsNuOdd95R7lOvuvf19eGKK65AY2MjzGYzamtrccQRR+Dbb78N2edLL72EyZMnw2q1orq6GmeeeSa2bdsW9bVs27YNJ5xwAkpLS1FTU4NrrrkGgUAgxicTyv33368c8+DBg3HJJZegu7s75P2+6aabAAA1NTWaa7ieffZZTJgwAdOmTcP06dPx7LPPRt1u27ZtOPfcczF48GCYzWY0NTXhoosugtfrxRNPPIFTTz0VADBt2jRFdsU/61jHEl4L09nZiWuuuQYTJ05EaWkpHA4HjjrqKHz//fea3iM1y5YtgyAIePLJJyPue/fddyEIAhYuXAhA+2etlcMOOwwAW5gAgp//unXrMGvWLNjtdvz2t78FwCbid911F3bbbTdYLBbU1dXhwgsvRFdXV8g+JUnCX//6VwwdOhQ2mw3Tpk3DTz/9FPHcsWqeli5dilmzZqGiogIlJSXYY489cPfddyvHd9999wEIlc5xMn2MyTB+/HhUV1dj3bp1Ibd7PB7cdNNNGDVqFMxmMxoaGnDdddeFjEOCIMDlcuHJJ59UXhP/vs2ZMydqhnTevHkhr53vJ9q4wWWbn332Ga666irU1NSgpKQEJ554Itrb20P2sWzZMsyYMQPV1dWwWq1oamrCOeeck9Z7QxBEalDmiSCItHjjjTcwYsQIHHjggZq2P++88/Dkk0/ilFNOwdVXX42lS5diwYIFWLlyJV577bWQbdeuXYvf/OY3uPDCC3HmmWfi9ttvx7HHHosHH3wQN9xwAy6++GIAwIIFC/DrX/8aq1evhk4XXBMKBAKYOXMm9t9/f/z973/HO++8g5tuugl+vx8333yzst3dd9+N4447Dr/97W/h9Xrxn//8B6eeeioWLlyIo48+OuSYPvzwQ7z44ouYO3cuqqurY0rMfv/73+Pll1/G3LlzMWHCBOzYsQNLlizBypUrsffeewNgNS9nn3029tlnHyxYsACtra24++678dlnn+G7774LyQAFAgHMmDED++23H26//XZ88MEHuOOOOzBy5EhcdNFFcd/zefPmYf78+Zg+fTouuugirF69Gg888AC+/vprfPbZZzAajbjrrrvw1FNP4bXXXsMDDzyA0tJS7LHHHnH36/F48Morr+Dqq68GAJxxxhk4++yzsX37dtTX1yvbNTc3Y99990V3dzcuuOACjBs3Dtu2bcPLL78Mt9uNQw45BJdddhn+9a9/4YYbbsD48eMBQLnUyvr16/H666/j1FNPRVNTE1pbW/HQQw9h6tSp+PnnnzF48GDN+5oyZQpGjBiBF198MSKT9sILL6CiokLJZGj5rJOBT/SrqqqU2/x+P2bMmIFf/epXuP322xU534UXXqh8jy677DJs2LAB9957L7777jvlswWAG2+8EX/9618xa9YszJo1C99++y2OPPJIeL3ehMfz/vvv45hjjsGgQYNw+eWXo76+HitXrsTChQtx+eWX48ILL0RzczPef/99PP300xGPz8UxxqKnpwddXV0YOXKkcpsoijjuuOOwZMkSXHDBBRg/fjx+/PFH/POf/8Qvv/yi1Dg9/fTTOO+887DvvvviggsuAICQ/SRDtHFj+fLlAIBLL70UFRUVuOmmm7Bx40bcddddmDt3Ll544QUALCN85JFHoqamBn/84x9RXl6OjRs34tVXX035fSEIIg0kgiCIFOnp6ZEASMcff7ym7ZcvXy4BkM4777yQ26+55hoJgPThhx8qtw0fPlwCIH3++efKbe+++64EQLJardKmTZuU2x966CEJgPTRRx8pt82ePVsCIF166aXKbaIoSkcffbRkMpmk9vZ25Xa32x1yPF6vV9p9992lww47LOR2AJJOp5N++umniNcGQLrpppuU/8vKyqRLLrkk5nvh9Xql2tpaaffdd5f6+/uV2xcuXCgBkG688caI13LzzTeH7GOvvfaSJk+eHPM5JEmS2traJJPJJB155JFSIBBQbr/33nslANJjjz2m3HbTTTdJAELem3i8/PLLEgBpzZo1kiRJUm9vr2SxWKR//vOfIdudddZZkk6nk77++uuIfYiiKEmSJL300ksRnyEn/L3lDB8+XJo9e7by/8DAQMhrlCRJ2rBhg2Q2m0Peuw0bNkgApMcffzzu67v++uslo9EodXZ2Krd5PB6pvLxcOuecc5TbEn3WsXj88cclANIHH3wgtbe3S1u2bJH+85//SFVVVZLVapW2bt0qSVLw8//jH/8Y8vhPP/1UAiA9++yzIbe/8847Ibfz78DRRx+tvN+SJEk33HCDBCDkPfzoo49CPge/3y81NTVJw4cPl7q6ukKeR72vSy65RIo2pcjGMcYCgHTuuedK7e3tUltbm7Rs2TJp5syZEgDpH//4h7Ld008/Lel0OunTTz8NefyDDz4oAZA+++wz5baSkpKozz179mxp+PDhEbfz31D4cUUbN/jnP3369JDXfOWVV0p6vV7q7u6WJEmSXnvtNQlA1N8PQRC5h2R7BEGkTG9vLwBoNgh46623AABXXXVVyO08cxFeGzVhwgQccMAByv/77bcfACZrGjZsWMTt69evj3jOuXPnKte5fMbr9eKDDz5Qbrdarcr1rq4u9PT04OCDD44qu5o6dSomTJiQ4JWyuqGlS5eiubk56v3Lli1DW1sbLr744pDah6OPPhrjxo2LWif2+9//PuT/gw8+OOprVvPBBx/A6/XiiiuuCMnKnX/++XA4HGnVoz377LOYMmWKYi5ht9tx9NFHh0j3RFHE66+/jmOPPVap61ETLnFKB7PZrLzGQCCAHTt2oLS0FGPHjk1JQnfaaafB5/OFrPC/99576O7uDnEWTPRZJ2L69OmoqalBQ0MDTj/9dJSWluK1117DkCFDQrYLzzC+9NJLKCsrwxFHHIGOjg7lb/LkySgtLcVHH30EIPgduPTSS0Pe7yuuuCLhsX333XfYsGEDrrjiiohaOC2fXS6OUc2jjz6Kmpoa1NbWYsqUKVi0aBGuu+66kDHnpZdewvjx4zFu3LiQY+JySX5MmSTeuHHBBReEvOaDDz4YgUAAmzZtAhCsQVy4cCF8Pl/Gj40giOSg4IkgiJRxOBwAWM2HFjZt2gSdThfh5FZfX4/y8nJlssBRB0gAUFZWBgBoaGiIent4DYVOp8OIESNCbhszZgwAhFhiL1y4EPvvvz8sFgsqKytRU1ODBx54AD09PRGvoampKdHLBMBqwVasWIGGhgbsu+++mDdvXkigw1/r2LFjIx47bty4iPfCYrGgpqYm5LaKioqI1xxOrOcxmUwYMWJExPNopbu7G2+99RamTp2KtWvXKn8HHXQQli1bhl9++QUA0N7ejt7eXuy+++4pPU8yiKKIf/7znxg9ejTMZjOqq6tRU1ODH374IepnmYhJkyZh3LhxinwKYJK96upqZaINJP6sE3Hffffh/fffx0cffYSff/5ZqdNTYzAYMHTo0JDb1qxZg56eHtTW1qKmpibkz+l0KgYg/DMePXp0yONrampQUVER99i4hDDVzy8Xx6jm+OOPx/vvv48333xTqT9yu90hCwdr1qzBTz/9FHE8fGzgx5RJ4o0b4eMcf738tz116lScfPLJmD9/Pqqrq3H88cfj8ccfj6gTJQgiN1DNE0EQKeNwODB48GCsWLEiqcdpzTbo9fqkbpfCjCC08Omnn+K4447DIYccgvvvvx+DBg2C0WjE448/jueeey5ie3WWKh6//vWvcfDBB+O1117De++9h3/84x+47bbb8Oqrr+Koo45K+jhjveZ88dJLL8Hj8eCOO+7AHXfcEXH/s88+i/nz52f1GMLNMm699Vb8+c9/xjnnnIO//OUvqKyshE6nwxVXXAFRFFN6jtNOOw233HILOjo6YLfb8b///Q9nnHFGiC19up/1vvvuGzUrp0adVeOIooja2tqYJh3hwXY+yPUxDh06FNOnTwcAzJo1C9XV1Zg7dy6mTZuGk046STmmiRMn4s4774y6j/DFmWjEGsNiGbjEGzcSjWeCIODll1/Gl19+iTfeeAPvvvsuzjnnHNxxxx348ssvUVpamvB4CYLIHBQ8EQSRFscccwwefvhhfPHFFyESu2gMHz4coihizZo1IWYAra2t6O7uxvDhwzN6bKIoYv369cqKMgAlI8KNHl555RVYLBa8++67MJvNynaPP/542s8/aNAgXHzxxbj44ovR1taGvffeG7fccguOOuoo5bWuXr06JIvBb8vUe6F+HnUWzuv1YsOGDcpEM1meffZZ7L777opDn5qHHnoIzz33HObPn4+amho4HI6EAXa8gLqioiLEGZAff0tLS8htL7/8MqZNm4ZHH3005Pbu7m5UV1cneEXROe200zB//ny88sorqKurQ29vL04//fSI7eJ91tli5MiR+OCDD3DQQQfFnZzz78CaNWtCvgPt7e0JM5fcIGHFihVxvyuxPr9cHGM8LrzwQvzzn//En/70J5x44okQBAEjR47E999/j8MPPzzhQk6s+6N9JwGknMnVwv7774/9998ft9xyC5577jn89re/xX/+8x+cd955WXtOgiAiIdkeQRBpcd1116GkpATnnXceWltbI+5ft26dYmk8a9YsAMBdd90Vsg1fAQ53tssE9957r3JdkiTce++9MBqNOPzwwwGwVV9BEEJWjDdu3Kg4bqVCIBCIkInV1tZi8ODBitRmypQpqK2txYMPPhgiv3n77bexcuXKjL0X06dPh8lkwr/+9a+QzNyjjz6Knp6elJ5ny5Yt+OSTT/DrX/8ap5xySsTf2WefjbVr12Lp0qXQ6XQ44YQT8MYbb2DZsmUR++LHxHsWRZuQjhw5Ep988knIbQ8//HDEKr9er4/IPr700ksR1u/JMH78eEycOBEvvPACXnjhBQwaNAiHHHKIcr+Wzzpb/PrXv0YgEMBf/vKXiPv8fr/yXk6fPh1GoxH33HNPyPsT/juMxt57742mpibcddddEZ+Nel+xPr9cHGM8DAYDrr76aqxcuRL//e9/lWPatm0bHnnkkYjt+/v74XK5Ql5XrO9kT08PfvjhB+W2lpaWCMfQTNDV1RXxvd5zzz0BgKR7BJEHKPNEEERajBw5Es899xxOO+00jB8/HmeddRZ23313eL1efP7553jppZeU3iiTJk3C7Nmz8fDDD6O7uxtTp07FV199hSeffBInnHACpk2bltFjs1gseOeddzB79mzst99+ePvtt/Hmm2/ihhtuUORCRx99NO68807MnDkTv/nNb9DW1ob77rsPo0aNCpkYJUNfXx+GDh2KU045BZMmTUJpaSk++OADfP3114rEzWg04rbbbsPZZ5+NqVOn4owzzlCsyhsbG3HllVdm5D2oqanB9ddfj/nz52PmzJk47rjjsHr1atx///3YZ599QhqKauW5556DJEk47rjjot4/a9YsGAwGPPvss9hvv/1w66234r333sPUqVMVa+iWlha89NJLWLJkCcrLy7HnnntCr9fjtttuQ09PD8xmMw477DDU1tbivPPOw+9//3ucfPLJOOKII/D999/j3XffjcgmHXPMMbj55ptx9tln48ADD8SPP/6IZ599NqLuLVlOO+003HjjjbBYLDj33HND5HNaPutsMXXqVFx44YVYsGABli9fjiOPPBJGoxFr1qzBSy+9hLvvvhunnHKK0hNswYIFOOaYYzBr1ix89913ePvttxNm5HQ6HR544AEce+yx2HPPPXH22Wdj0KBBWLVqFX766Se8++67AIDJkycDAC677DLMmDEDer0ep59+ek6OMRFz5szBjTfeiNtuuw0nnHACfve73+HFF1/E73//e3z00Uc46KCDEAgEsGrVKrz44ot49913FRnl5MmT8cEHH+DOO+/E4MGD0dTUhP322w+nn346/vCHP+DEE0/EZZddBrfbjQceeABjxoxJub9XLJ588kncf//9OPHEEzFy5Ej09fXhkUcegcPhUBakCILIIXly+SMIYifjl19+kc4//3ypsbFRMplMkt1ulw466CDpnnvukQYGBpTtfD6fNH/+fKmpqUkyGo1SQ0ODdP3114dsI0nMhvroo4+OeB4AEbbQ3HpabUc8e/ZsqaSkRFq3bp105JFHSjabTaqrq5NuuummCDvrRx99VBo9erRkNpulcePGSY8//nhMy+FYltRQ2Wl7PB7p2muvlSZNmiTZ7XappKREmjRpknT//fdHPO6FF16Q9tprL8lsNkuVlZXSb3/7W8WiOvy1hBPtGGNx7733SuPGjZOMRqNUV1cnXXTRRRHW01qtyidOnCgNGzYs7jaHHnqoVFtbK/l8PkmSJGnTpk3SWWedJdXU1Ehms1kaMWKEdMkll0gej0d5zCOPPCKNGDFC0uv1IXbZgUBA+sMf/iBVV1dLNptNmjFjhrR27dqoVuVXX321NGjQIMlqtUoHHXSQ9MUXX0hTp06Vpk6dqmyn1aqcs2bNGgmABEBasmRJyH3JfNbhcKvqRBbUsT5/zsMPPyxNnjxZslqtkt1ulyZOnChdd911UnNzs7JNIBCQ5s+fr7w3hx56qLRixYqI9zDcqpyzZMkS6YgjjlBe4x577CHdc889yv1+v1+69NJLpZqaGkkQhIjvZSaPMRbxfp/z5s0LeV1er1e67bbbpN12200ym81SRUWFNHnyZGn+/PlST0+P8rhVq1ZJhxxyiGS1WiMs09977z1p9913l0wmkzR27FjpmWeeSWrciPX5h38G3377rXTGGWdIw4YNk8xms1RbWysdc8wx0rJlyxK+JwRBZB5BklKosCYIgihw5syZg5dffhlOpzPfh0IQBEEQxE4C1TwRBEEQBEEQBEFogIIngiAIgiAIgiAIDVDwRBAEQRAEQRAEoQGqeSIIgiAIgiAIgtAAZZ4IgiAIgiAIgiA0QMETQRAEQRAEQRCEBnbJJrmiKKK5uRl2ux2CIOT7cAiCIAiCIAiCyBOSJKGvrw+DBw8OaYQejV0yeGpubkZDQ0O+D4MgCIIgCIIgiAJhy5YtGDp0aNxtdsngyW63A2BvkMPhyPPREARBEARBEASRL3p7e9HQ0KDECPHYJYMnLtVzOBwUPBEEQRAEQRAEoamchwwjCIIgCIIgCIIgNEDBE0EQBEEQBEEQhAYoeCIIgiAIgiAIgtDALlnzRBAEQRAEQWSGQCAAn8+X78MgiJgYjUbo9fqM7IuCJ4IgCIIgCCJpJEnC9u3b0d3dne9DIYiElJeXo76+Pu0erxQ8EQRBEARBEEnDA6fa2lrYbLa0J6UEkQ0kSYLb7UZbWxsAYNCgQWntj4IngiAIgiAIIikCgYASOFVVVeX7cAgiLlarFQDQ1taG2tratCR8ZBhBEARBEARBJAWvcbLZbHk+EoLQBv+uplufR8ETQRAEQRAEkRIk1SOKhUx9Vyl4IgiCIAiCIAiC0AAFTwRBEARBEARRIAiCgNdffz3fh0HEgIIngiAIgiAIYpfjiy++gF6vx9FHH530YxsbG3HXXXdl/qA0MGfOHAiCAEEQYDQa0dTUhOuuuw4DAwOa97F48WIIgkA28ylAwRNBEARBEASxy/Hoo4/i0ksvxSeffILm5uZ8H05SzJw5Ey0tLVi/fj3++c9/4qGHHsJNN92Ul2PZ1RokU/BEEARBEARB7FI4nU688MILuOiii3D00UfjiSeeiNjmjTfewD777AOLxYLq6mqceOKJAIBDDz0UmzZtwpVXXqlkgABg3rx52HPPPUP2cdddd6GxsVH5/+uvv8YRRxyB6upqlJWVYerUqfj222+TPn6z2Yz6+no0NDTghBNOwPTp0/H+++8r94uiiAULFqCpqQlWqxWTJk3Cyy+/DADYuHEjpk2bBgCoqKiAIAiYM2cOgOgZtT333BPz5s1T/hcEAQ888ACOO+44lJSU4JZbblFe+9NPP43GxkaUlZXh9NNPR19fn/K4l19+GRMnToTVakVVVRWmT58Ol8uV9GvPNxQ8EQRBEARBEGkjSRLcXn9e/iRJSupYX3zxRYwbNw5jx47FmWeeicceeyxkH2+++SZOPPFEzJo1C9999x0WLVqEfffdFwDw6quvYujQobj55pvR0tKClpYWzc/b19eH2bNnY8mSJfjyyy8xevRozJo1KyTISJYVK1bg888/h8lkUm5bsGABnnrqKTz44IP46aefcOWVV+LMM8/Exx9/jIaGBrzyyisAgNWrV6OlpQV33313Us85b948nHjiifjxxx9xzjnnAADWrVuH119/HQsXLsTChQvx8ccf429/+xsAoKWlBWeccQbOOeccrFy5EosXL8ZJJ52U9OdWCFCTXIIgCIIgCCJt+n0BTLjx3bw89883z4DNpH1a++ijj+LMM88EwCRwPT09+Pjjj3HooYcCAG655RacfvrpmD9/vvKYSZMmAQAqKyuh1+tht9tRX1+f1HEedthhIf8//PDDKC8vx8cff4xjjjlG834WLlyI0tJS+P1+eDwe6HQ63HvvvQAAj8eDW2+9FR988AEOOOAAAMCIESOwZMkSPPTQQ5g6dSoqKysBALW1tSgvL0/qNQDAb37zG5x99tkht4miiCeeeAJ2ux0A8Lvf/Q6LFi3CLbfcgpaWFvj9fpx00kkYPnw4AGDixIlJP28hQJkngiAIgiAIYpdh9erV+Oqrr3DGGWcAAAwGA0477TQ8+uijyjbLly/H4YcfnvHnbm1txfnnn4/Ro0ejrKwMDocDTqcTmzdvTmo/06ZNw/Lly7F06VLMnj0bZ599Nk4++WQAwNq1a+F2u3HEEUegtLRU+Xvqqaewbt26jLyOKVOmRNzW2NioBE4AMGjQILS1tQFggefhhx+OiRMn4tRTT8UjjzyCrq6ujBxLrqHME0EQBcG3m7vwyCfrcf1R4zGsijrW54u23gHodAKqS835PhSCIIoMq1GPn2+ekbfn1sqjjz4Kv9+PwYMHK7dJkgSz2Yx7770XZWVlsFqtSR+DTqeLkKGFmynMnj0bO3bswN13343hw4fDbDbjgAMOgNfrTeq5SkpKMGrUKADAY489hkmTJuHRRx/FueeeC6fTCYBJD4cMGRLyOLM5/tiu5TXw5w/HaDSG/C8IAkRRBADo9Xq8//77+Pzzz/Hee+/hnnvuwf/93/9h6dKlaGpqSvBqCwvKPBEEURDc++FavL1iO974obgcj3YmvH4RM+76BLPu/hQBsfh06ARB5BdBEGAzGfLyx00bEuH3+/HUU0/hjjvuwPLly5W/77//HoMHD8bzzz8PANhjjz2waNGimPsxmUwIBAIht9XU1GD79u0hwcfy5ctDtvnss89w2WWXYdasWdhtt91gNpvR0dGh8R2Ojk6nww033IA//elP6O/vx4QJE2A2m7F582aMGjUq5K+hoUE5fgBRX4O6hqu3txcbNmxI6/g4giDgoIMOwvz58/Hdd9/BZDLhtddey8i+cwkFTwRB5J2AKOHrDZ0AAJfHn+ej2XXpdnvR5fahrc+D3v5dy3qWIIhdg4ULF6Krqwvnnnsudt9995C/k08+WZHu3XTTTXj++edx0003YeXKlfjxxx9x2223KftpbGzEJ598gm3btinBz6GHHor29nb8/e9/x7p163Dffffh7bffDnn+0aNH4+mnn8bKlSuxdOlS/Pa3v00pyxXOqaeeCr1ej/vuuw92ux3XXHMNrrzySjz55JNYt24dvv32W9xzzz148sknAQDDhw+HIAhYuHAh2tvblWzVYYcdhqeffhqffvopfvzxR8yePRt6vfasXiyWLl2KW2+9FcuWLcPmzZvx6quvor29HePHj09737mGgieCIPLOz8296JODJrc3kGBrIluo3/seCp4IgtgJefTRRzF9+nSUlZVF3HfyySdj2bJl+OGHH3DooYfipZdewv/+9z/sueeeOOyww/DVV18p2958883YuHEjRo4ciZqaGgDA+PHjcf/99+O+++7DpEmT8NVXX+Gaa66JeP6uri7svffe+N3vfofLLrsMtbW1ab8ug8GAuXPn4u9//ztcLhf+8pe/4M9//jMWLFiA8ePHY+bMmXjzzTcVidyQIUMwf/58/PGPf0RdXR3mzp0LALj++usxdepUHHPMMTj66KNxwgknYOTIkWkfn8PhwCeffIJZs2ZhzJgx+NOf/oQ77rgDRx11VNr7zjWCVIwegWnS29uLsrIy9PT0wOFw5PtwCGKX55FP1uOWt1YCAH49ZSj+fsqkPB/RrslPzT04+l9LAAD/veQgTGooz+8BEQRRsAwMDGDDhg1oamqCxWLJ9+EQRELifWeTiQ0o80QQRN75cv0O5bqLMk95Q5156qbME0EQBEFEQMETQRB5JSBK+EqudwKAfgqe8gbJ9giCIAgiPhQ8EQSRV1a2BOudADKMyCdu1XtPwRNBEARBRELBE0EQeYVL9ixGNhz1+yjzlC9CMk/u5HqOEARBEMSuAAVPBEHkFR48HTiyGgC57eUTt5cyTwRBEAQRDwqeCILIGwFRwlK53mnaOGbV6ibZXt5wUc0TQRAEQcSFgieCIPLGypZe9A34YTcbsE9jBQDATbK9vBHituem4IkgCIIgwqHgiSCIvMEle/s0VcJuMQIA3B4KnvIFGUYQBEEQRHwoeCIIIm98uZ5J9vZrqkSJSQ8A8AZE+AJiPg9rl0Wd9aPgiSAIgiAioeCJIIi8wPo7sczT/iOqYJWDJ4BMI/IFZZ6IQkJtYEIQxcqcOXNwwgknKP8feuihuOKKK3J+HIsXL4YgCOju7s75c+9sUPBEEEReWNnSi94BP0rNBuw22AGTXgeDTgBAjXLzBRlGEIXCh6tasftN7+LJzzfm+1CInZA5c+ZAEAQIggCTyYRRo0bh5ptvht+f/YD91VdfxV/+8hdN2+Y64GlsbFTeF5vNhokTJ+Lf//53UvuYN28e9txzz+wcYIFAwRNBEHmBu+zt01gBg14HQRCU7JOLVpzzgjpodXsD8PpJPknkh+82d0OUgO82d+X7UIidlJkzZ6KlpQVr1qzB1VdfjXnz5uEf//hH1G293sz1vausrITdbs/Y/jLNzTffjJaWFqxYsQJnnnkmzj//fLz99ts5Pw5JknISzKYCBU8EQeQFbhax34gq5TabHDxR5ik/hAetlH0i8kXfAPsuOql1AZElzGYz6uvrMXz4cFx00UWYPn06/ve//wEISu1uueUWDB48GGPHjgUAbNmyBb/+9a9RXl6OyspKHH/88di4caOyz0AggKuuugrl5eWoqqrCddddB0mSQp43XLbn8Xjwhz/8AQ0NDTCbzRg1ahQeffRRbNy4EdOmTQMAVFRUQBAEzJkzBwAgiiIWLFiApqYmWK1WTJo0CS+//HLI87z11lsYM2YMrFYrpk2bFnKc8bDb7aivr8eIESPwhz/8AZWVlXj//feV+7u7u3HeeeehpqYGDocDhx12GL7//nsAwBNPPIH58+fj+++/VzJYTzzxBDZu3AhBELB8+fKQ/QiCgMWLFwMIZtnefvttTJ48GWazGUuWLMGhhx6Kyy67DNdddx0qKytRX1+PefPmKfuRJAnz5s3DsGHDYDabMXjwYFx22WWaXmuqGLK6d4IgiCiIooSv5MzT/qrgqcRkAOCBiyZMeSHc6bCn34cauzlPR0PsyvCgiYKnIkOSAJ87P89ttAGCkPLDrVYrduzYofy/aNEiOBwOJXDw+XyYMWMGDjjgAHz66acwGAz461//ipkzZ+KHH36AyWTCHXfcgSeeeAKPPfYYxo8fjzvuuAOvvfYaDjvssJjPe9ZZZ+GLL77Av/71L0yaNAkbNmxAR0cHGhoa8Morr+Dkk0/G6tWr4XA4YLVaAQALFizAM888gwcffBCjR4/GJ598gjPPPBM1NTWYOnUqtmzZgpNOOgmXXHIJLrjgAixbtgxXX311Uu+HKIp47bXX0NXVBZPJpNx+6qmnwmq14u2330ZZWRkeeughHH744fjll19w2mmnYcWKFXjnnXfwwQcfAADKysrQ2tqq+Xn/+Mc/4vbbb8eIESNQUcFamDz55JO46qqrsHTpUnzxxReYM2cODjroIBxxxBF45ZVX8M9//hP/+c9/sNtuu2H79u1KMJctKHgiCCLnrNzei55+H0pMeuw+2KHczmV71OspP7h94ZmnzElVCCIZnHLmyUWtC4oLnxu4dXB+nvuGZsBUkvTDJEnCokWL8O677+LSSy9Vbi8pKcG///1vJXB45plnIIoi/v3vf0OQg7THH38c5eXlWLx4MY488kjcdddduP7663HSSScBAB588EG8++67MZ/7l19+wYsvvoj3338f06dPBwCMGDFCub+yshIAUFtbi/LycgAsU3Xrrbfigw8+wAEHHKA8ZsmSJXjooYcwdepUPPDAAxg5ciTuuOMOAMDYsWPx448/4rbbbkv4fvzhD3/An/70J3g8Hvj9flRWVuK8884DACxZsgRfffUV2traYDazhbXbb78dr7/+Ol5++WVccMEFKC0thcFgQH19fcLnisbNN9+MI444IuS2PfbYAzfddBMAYPTo0bj33nuxaNEiHHHEEdi8eTPq6+sxffp0GI1GDBs2DPvuu29Kz60VCp4Igsg5S2WL8n2aKmHQB9XDLPNEsr18wTNPJoMOXr9Isj0ib/CME2WhiWyxcOFClJaWwufzQRRF/OY3vwmRg02cODEk4/L9999j7dq1EfVKAwMDWLduHXp6etDS0oL99ttPuc9gMGDKlCkR0j3O8uXLodfrMXXqVM3HvXbtWrjd7ogAw+v1Yq+99gIArFy5MuQ4ACiBViKuvfZazJkzBy0tLbj22mtx8cUXY9SoUQDYe+B0OlFVVRXymP7+fqxbt07za4jHlClTIm7bY489Qv4fNGgQ2traALBM2F133YURI0Zg5syZmDVrFo499lgYDNkLcSh4Iggi5/B6J7VkDwhmnmjClB+4RfzgMgs27nBT8ETkjb4B9t0j2V6RYbSxDFC+njsJpk2bhgceeAAmkwmDBw+OmGyXlIRmsZxOJyZPnoxnn302Yl81NTXJHy+gyPCSwel0AgDefPNNDBkyJOQ+ng1Kh+rqaowaNQqjRo3CSy+9hIkTJ2LKlCmYMGECnE4nBg0apNQpqeGZsWjodGyRVB1E+nzRzy/h7zsAGI3GkP8FQYAoMkOjhoYGrF69Gh988AHef/99XHzxxfjHP/6Bjz/+OOJxmYKCJ4IgcoooSorT3n5NlSH3lZhlwwiS7eWcgCgp7/ugMis27nCj203BE5Ef+ijzVJwIQkrSuXxQUlKiZFS0sPfee+OFF15AbW0tHA5H1G0GDRqEpUuX4pBDDgEA+P1+fPPNN9h7772jbj9x4kSIooiPP/5Yke2p4ZmvQCB4TpwwYQLMZjM2b94cM2M1fvx4xfyC8+WXXyZ+kWE0NDTgtNNOw/XXX4///ve/2HvvvbF9+3YYDAY0NjZGfYzJZAo5XiAYXLa0tCjZMbV5RLpYrVYce+yxOPbYY3HJJZdg3Lhx+PHHH2O+7+lCbnsEQeSUVdv7gvVOQ8pC7rMa2XoO1TnkHnXAOqjcAoDc9oj8odQ8eQMQxeiSJ4LIJb/97W9RXV2N448/Hp9++ik2bNiAxYsX47LLLsPWrVsBAJdffjn+9re/4fXXX8eqVatw8cUXx+3R1NjYiNmzZ+Occ87B66+/ruzzxRdfBAAMHz4cgiBg4cKFaG9vh9PphN1uxzXXXIMrr7wSTz75JNatW4dvv/0W99xzD5588kkAwO9//3usWbMG1157LVavXo3nnnsOTzzxREqv+/LLL8cbb7yBZcuWYfr06TjggANwwgkn4L333sPGjRvx+eef4//+7/+wbNky5TVt2LABy5cvR0dHBzweD6xWK/bff3/87W9/w8qVK/Hxxx/jT3/6U0rHE84TTzyBRx99FCtWrMD69evxzDPPwGq1Yvjw4RnZfzQoeCIIIqcs3cAke1MaK2HUhw5BSuaJ+jzlHLf8ngsCUGun4InIL2q5HvV9IwoBm82GTz75BMOGDcNJJ52E8ePH49xzz8XAwICSibr66qvxu9/9DrNnz8YBBxwAu92OE088Me5+H3jgAZxyyim4+OKLMW7cOJx//vlwuVwAgCFDhmD+/Pn44x//iLq6OsydOxcA8Je//AV//vOfsWDBAowfPx4zZ87Em2++iaamJgDAsGHD8Morr+D111/HpEmT8OCDD+LWW29N6XVPmDABRx55JG688UYIgoC33noLhxxyCM4++2yMGTMGp59+OjZt2oS6ujoAwMknn4yZM2di2rRpqKmpwfPPPw8AeOyxx+D3+zF58mRcccUV+Otf/5rS8YRTXl6ORx55BAcddBD22GMPfPDBB3jjjTci6rIyiSDFqmLbient7UVZWRl6enpipl4JgsgOFz69DO/+1Io/zByHiw4dGXLfgrdX4qGP1+PcXzXhz8dMyNMR7pps7HDh0NsXo9RswKWHjcKCt1fhpL2G4M7T9sz3oRG7GAFRwsgb3lL+//L6w1FfZsnjERHRGBgYwIYNG9DU1ASLhT4fovCJ951NJjagzBNBEDkjpN5pRGXE/dxtz01uezmHv+dWkx5lVlZkS5knIh+Em0SQaQRBEIUEBU8EQeSM1a196Hb7YDPpMTGs3gkAbLzPE8l0cg5/z0soeCLyDHfa45BpBEEQhQQFTwRB5AxuUR6t3glQNcmlzFPOcSmZJwPKbCx46qbgicgDlHkiCKKQoeCJIIicwZvj7h9FsgeoZXs0Wco1/ZR5IgoE7rSn/E/BE0EQBUROgqf77rsPjY2NsFgs2G+//fDVV1/F3Pann37CySefjMbGRgiCgLvuuivtfRIEkX9YvRPLPO3XFN0FhzJP+YPbw9vMhpDgaRf0FCLyTF9YsESyPYIgComsB08vvPACrrrqKtx000349ttvMWnSJMyYMQNtbW1Rt3e73RgxYgT+9re/ob6+PiP7JAgi//zS1ocutw9Wox57DI2sdwKCmad+Cp5yDs/22Yx6lNtYY0avX8SAT8znYRG7IOGZJwqeChtRpDGCKA4y9V01ZGQvcbjzzjtx/vnn4+yzzwYAPPjgg3jzzTfx2GOP4Y9//GPE9vvssw/22WcfAIh6fyr7JAgi/3y5jtc7VUStdwKCmSfq65J7eLbPZtajxKSHXicgIEro6fcpnwtB5ILImidaTClETCYTdDodmpubUVNTA5PJBEEQ8n1YBBGBJEnwer1ob2+HTqeDyWRKa39ZDZ68Xi+++eYbXH/99cptOp0O06dPxxdffJGzfXo8Hng8HuX/3t7elJ6bIIjU4Rbl+4+I3bgu2CSXJku5hhtGlJgMEAQB5VYjdri86On3UY8dIqeQ215xoNPp0NTUhJaWFjQ3N+f7cAgiITabDcOGDYNOl57wLqvBU0dHBwKBgNJ1mFNXV4dVq1blbJ8LFizA/PnzU3o+giDSR93fKZZZBADYjGxIctFKc87hhhHcLr5MDp663d58HhaxC0KGEcWDyWTCsGHD4Pf7EQjQuE0ULnq9HgaDISPZ0azL9gqB66+/HldddZXyf29vLxoaGvJ4RASxa7GmzYlOlxdWox4Th5TH3M7GM0++AERRgk5HEpBcwTNPNrnuzEGOe0Se4IYRXDpKwVNhIwgCjEYjjEZjvg+FIHJCVoOn6upq6PV6tLa2htze2toa0wwiG/s0m80wm80pPR9BEOkT7O9UAZMhdrrcpqqt6fcFUGLeJdZ3CgK3JzTzVE69nog8wTNPtXYzWnoGSLZHEERBkVW3PZPJhMmTJ2PRokXKbaIoYtGiRTjggAMKZp8EQWQXblEer94JACwGPXhGnezKc4vaMAKAYlfeS8ETkWN4pqnOYQn5nyAIohDI+rLuVVddhdmzZ2PKlCnYd999cdddd8HlcilOeWeddRaGDBmCBQsWAGCGED///LNyfdu2bVi+fDlKS0sxatQoTfskCKJwkCQJX8rNcfdril3vBAA6nQCrUQ+3NyBbZ1PGOFe4VYYRAKhRLpE3eLBULwdPlHkiCKKQyHrwdNppp6G9vR033ngjtm/fjj333BPvvPOOYviwefPmENeL5uZm7LXXXsr/t99+O26//XZMnToVixcv1rRPgiAKB17vZDHqsMfQ8oTb20w8eKLMUy7hfZ64LXm5HDx1uyl4InJLnyzb4y6PlHkiCKKQyElBwdy5czF37tyo9/GAiNPY2Kipo328fRIEUTgo9U7DK+PWO3GYYYFXmcwTuSE880SGEUS+4FblPHgi902CIAqJrNY8EQRB8OApnkW5Gm5YQJmn3OIKyzyRbI/IF+GyPco8EbsyPW4frnpxOT5f15HvQyFkyMqKIIisIUkSlvJ6pwRmERwKnvIDb0zMGxWX21gHdnLbI3KNM0y25/L4IUlSRvqzEESx8daKFrz67TZs7xnAgSOr8304BCjzRBBEFlnb5sQOpd6pTNNjeJ8hku3lFi6NCjeMILc9IpcEREnpOcYzT35Rgscv5vOwCCJvbO1yAwC2dffn+UgIDgVPBEFkja82sqzT3sMqYDboE2zNoMxT7hFFCf0+9n6TbI/IJy7Vogm3KgfIcY/YddnWxYKmlu4BiGJiTwAi+1DwRBBE1tjeMwAAGFlTqvkxSvBEReI5gwdOQDDzxJvk9vT7NJn4EEQm4JI9k14Hq0mvjAdU90TsqvCMkzcgosPlyfPREAAFTwRBZJEutxcAUCFPxLVgM3PZHgVPuYKv9gsCYDGy0wLPPAVEiSauRM7gNuWlFjYOlMjjAX0HiV0VnnkCgObugfgb93cD3zwJ9Hdl96B2cSh4Iggia3TJPYIqSkyaH2MzctkeTZZyBTeLsBn1SlG+xahXrOVJukfkCqeHfddK5aCJX5Jdeeb496frcdgdixVlAFG4+AMitvcGP6fmRHVPSx8C3rgM+PKBLB/Zrg0FTwRBZI1uJfOURPBENU85h09MedaPQ41yiVyjZJ7MPPPExgOqecoc/13ejPXtLizdsCPfh0IkYHvvANRlTgmDp8717LJnW/YOiqDgiSCI7NHlYpPu8hRkey7KPOUMnuXjgSuHHPeIXMPleXYu2zORbC/TdLrYohZllAsftWQP0OC419fCLkm2l1UoeCKKipe/2Yor/vMdvGRbWxSkk3nqp8xTzuBZPm4TzyHHPSLXcMMIHjzxSwqeMgcfl2lRpPAJD5YSZp76trPLge7sHBABgIInosi498M1eH15M77ZRKsqxYBS85RU8MQzTxQ85QqeeSoJyzzxjCE1yiVyBQ+SgrI9XvNEwVMm8PgDythKiyKFD888Vcl1wwkNI3jw1N+dxaMiKHgiigo+iaNBv/AZ8AUUC+zykiRke0rmiSZLuYJnnqxhwZODMk9Ejuklt72soq5fpN914dPcw4KnfRorASSQ7XldgKeHXSfZXlah4IkoGiRJUmQGvQM06Bc63KbcoBNgDzMiiAcZRuQevhJdQrI9Is84FcMIo3xJmadMwsdlAOjtp/e00NkqZ572aWLBU6fLG1vSzrNOAMn2sgwFT0TR4PIGFNcZ7shEFC5qswhuf60FLtuj4Cl3uD3RDSPKrUwqQm57RK7gVuWRhhE0HmQCPi4DtChSDPBM0/h6u7KQwLNREaiDJ58b8FND3WxBwRNRNKgHeip0LXx4UXJ5EvVOgDrzRAFyrlAMI8zhbnvsZE2/NyJXRLjtkVV5RulWZZ4oeCpsJElSDCKGVFgxuNwCII5pBHfa41DdU9ag4IkoGtQTOMo8FT5Bswjt9U6AKniileacETSMCJPt2Ui2R+SW8D5P5LaXWTopeCoaOl1eDPhECAIwqMyKweVWAPGCp+2h/5N0L2tQ8EQUDergiWqeCp+uFDNPvEDc7QtAkqQEWxOZIJZhhCLb6/dGPIYgskEstz0KnjKDWoJL59HChkv2au1mmAw6JXjaFstxjzJPOYOCJ6JoINleccHlIZVJBk98Ah8QJXion1dOcMcwjCC3PSLX9MVw2yPZXmbocgUXQvoG/AiItEBVqHCb8iFy0DSEMk8FAwVPRNHQq5LqkWyv8OGyvWRsygHAZgxmP6hRbm7gE9PImie5zxMZRhA5QmmSS257WUEt2wOAPso+FSw888QzTolrnsKCJ7IrzxoUPBFFA8n2igsu20umQS4AGPQ6mAxsaHKRaURO4P24Itz25JonWqEmcoUi2yO3vawQvhBCduWFC7cpH1IhB09liTJPsmyvtI5dkmwva1DwRBQN6oCJMk+FT3eKhhGAulEuTZhygZJ5itHnCaAVaiL7iKIU4bYXNIyg718m6ArLPJEkt3DhQdJQJfMkB089AxDDF7MkKZh5qhnHLkm2lzUoeCKKhh7KPBUVqRpGAMHVZur1lBsUq/KwzJNRr0OJfBtJ94hso840hxtGDPhE+ANUA5ku4b9jCp4Kl23doZmn+jILBAHw+kXscIWZ+Hj6AJ+LXa8dzy5Jtpc1KHgiiga1vKBvwE9ObAVOMPOUfPDETSNItpcbgsGTIeK+MjKNIHIEzzoZ9QLMsnS3RFWH56LFlLTpdHE5Nftd00Jk4aIET+U2AGwxq84eo+6JZ53MDsAxmF0n2V7WoOCJKBrUg3xAlCgrUeCEn6SToYRkezlF6fMUZhgBkOMekTvUPZ4EQQAAmA16GPXsOplGpEdAlJTz6PCqEgD0uy5UXB6/sgDJjSLU1yODJ7neyV4PWMrZdZLtZQ0KnoiiIdyenFbMChf1SToV2V4w80TBUy5QMk/GyMwTN43opkkWkWXCbco5ZFeeGXr6feCCjeFVNuU2ovDgWSeHxQC7JbgAGez1FCPzZK8HrBXsOsn2sgYFT0TRED7Ik2lE4aI+SZenlHlik6V+ku1lHVGVxQ23KgdItkfkjmCD3NAxg48HfRQ8pQVXA9gtBlSWsEUt6plYmATrnWwht/P6p8jgiWeeBgHWcnadZHtZg4InomgID5Zo0C9cuFmE3WyAUZ/8MKNknsieOOtwm3Ig0jACCAZP9Hsjsk2wx1No5ok77lHmKT26Ve0jHBZaFClkwhvkcmI2ylVnnrIl2xPJsIVDwRNRNPDJGz+xUuapcOEn6WQb5HIUq3IfBU/ZhmedBAGwGCKDJy677A6zOCaITMPtyO0k28sKXar2EZRRLmyCZhGWkNuDvZ4GQh8Qknnisr1uIFPGWguvBO4cD7h2ZGZ/RQ4FT0RREBAlRbLB09ZU81S4dLnYZ1OZQr0TEHR9o8lS9uFmETajHjqdEHE/TbKIXJGo5oka5aaH0ri8xES/6wJnW1iDXM5gLZknLtsLeABfjIa6ybL6bcC5HWj+LjP7K3IoeCKKAnWDzqE8eKJBv2BJp8cTEMw8kaNi9uHvsTWKTTlAbntE7gjWPIV+F0vNXMZLiynp0OVSyfa4HJcUHAVJc5hNOYfL9na4vBhQKzPUmSdTKSDIKoJMSPckCXDLGSdXW/r72wmg4IkoCniPJ5tJr/QNokG/cOlWyUNSga80u8kwIuvEsykHgHJ5kkVNcolsEzPzZOKZJxoP0oHL9spVsj1ahCxMwhvkchxWg9LKQ8k+SVJo5kkQMmsa4XUCAVm27WpPf387ARQ8EUUBl+g5LEbVihkN+oVKupknq5EyT7mCm3JEa5ALkGyPyB2xDCN4MEXBU3rwusVKG8n2ChlfQERrL6tpGhxW8yQIgkq6J9c99XcxiR4AlNazy0zalbtVdU4UPAGg4IkoEvgA77AaFJcgMowoXIKFyakFTzwLQk1ys49iUx7FaQ8gtz0id8SW7VENZCbgVuXlJSY4rOw97e33QcqUqQCREbb3DECUAJNBh+oSc8T9EXVPPOtkrQCMcrCVScc9dfDkpOAJoOCJKBL4xK3MalScmGgyV7golrgpuu3x+hsXyfayjmIYESN4oia5RK7gpkDqpqCA2jCCxoN06I7itudX9XkjCoOtKpvyaCY+EY1y1fVOnEzK9tydweuUeQJAwRNRJESX7dGJtFBRVjhTzTyZKPOUK1waM09ubwC+APX5ILKHUx7nY7ntUeYpPbpUfZ6sRj2MejYxJ+leYRE0i7BGvZ/bl0dknuz1wY2yJtsjwwiAgieiSOCGEQ5V5qmPap4KlnQNI5QmuRQ8ZZ1+bhgRo+ZJnQWgSRaRTXhmKaLmyUxNszOBWk4tCIIigaf64cJiW4LgSZHt9cTJPGVLtufqSH9/OwEUPBFFgVLzZAnWPJFsr3BRr3CmAp/IU+Yp+yiGETHc9vQ6AQ55wYIc94hskshtr48yTykjSVKEnFoxjaDfdUHBezwNThQ8ccOIqJmncnaZEdlemGGESAoECp6IooCvjJVZjUqhKxlGFCbsJB20xE0Fm5J5os842/T74rvtAUCZjZy5iOzD3fYiDCMsJNtLlz6PH36RGUPwRS3q4VaYxLIp5wxR1TxJkpTbzJPoz8w+ixwKnoiioFdx2zOS1KDAcXsD8Mq1MalmnqzUJDdn8AlprJongBz3iOwjihKc3uiZJ3LbS59uF/vtWo16WORWEGRXXpgkku3Vl1kgCIDXL6LD6c1tzRNAphGg4IkoEoKyvWDwNOAT4fVT+rjQ4JI9k0EXd0IeDy7T8fpF+MmkIKsksioHgHIrC4K7+705OSZi18PtC4A7ZjvIbS/jdCpS6uB7S+ZLhYckSUrwNDRG5smo16HOrjKNUIKnHLjtARQ8gYInokjgg7vDagxZlSTTiMJDbRYhCJE2q1qwqibybh9ln7JJ0Ko8jmxvJ6iNmPe/nzDr7k8pe1GgcMmeQSfAbAidmqgzT9STKDWiNS4vkyXwlHkqHDqcXnj9IgQBqHNYYm7Hm+c2d7kAZ5TMUzZke4L8u3SS4x4FT0RR0KtqkqvXCcrJlFbMCo90zSIAwGzQQS/********************************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 预测\n", "x_text_ = torch.from_numpy(X_test).float()\n", "y_test_ = torch.from_numpy(y_test).float()\n", "y_pred_ = tcn(x_text_)\n", "# 转为numpy数组\n", "print(type(y_test_))\n", "print(type(y_pred_))\n", "y_test_np = y_test_.detach().numpy()\n", "y_pred_np = y_pred_.detach().numpy()\n", "# 画图对比\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(y_test_np, label='Actual Returns')\n", "plt.plot(y_pred_np, label='Predicted Returns')\n", "plt.title('Comparison of Actual vs Predicted Returns')\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}