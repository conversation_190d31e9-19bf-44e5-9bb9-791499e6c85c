from common.config import Config
from common.data_processor import DataProcessor
from common.trainer import ModelTrainer
from common.visualizer import Visualizer
import torch
from torch.nn.utils.parametrizations import weight_norm
from torch.utils.data import TensorDataset, DataLoader
from pytorch_tcn import TCN

if __name__ == '__main__':
	# 设置训练设备
	device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
	print(f"Using device: {device}")
	# 读取数据
	config = Config(input_window=20, model_path='model_tcn.pth')
	dp = DataProcessor(config, device)
	df = dp.load_data('./big_data/WLD_USDT_USDT.csv')
	# 构造特征
	X = dp.build_features(df)
	# 数据标准化
	normalized_X = dp.normalize_data(X)
	# 划分数据集
	train_loader, val_loader, test_loader = dp.build_dataloader(normalized_X)
	# 训练模型
	num_inputs = len(normalized_X.columns)
	print('num_inputs: ', num_inputs)
	# out_seq_len = ((in_seq_len + 2*padding - kernel_size) / 卷积步长) + 1 = 19 - 4 + 1 = 16
	model = TCN(num_inputs, (32, 32, 32, 32)).to(device)
	trainer = ModelTrainer(model, config)
	trainer.train(train_loader, val_loader)
	'''
	conv1: 期望的输入是 (输出通道数, 输入通道数, 卷积核大小), 如(32, 3, 4)
	而传入的输入是 (batch_size, seq_len, input_dim), 如(128, 19, 6)
	'''
	# 验证模型
	y_test_pred, y_test_true = trainer.evaluate(test_loader)
	# 数据反标准化
	base_prices = dp.features['BasePrice'].values[-config.test_set_size-1:-1]
	pred_prices = dp.denormalize_data(y_test_pred, base_prices)
	orig_prices = dp.denormalize_data(y_test_true, base_prices)
	# 数据可视化
	Visualizer.plot_results(orig_prices, pred_prices)
