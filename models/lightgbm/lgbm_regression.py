"""
LightGBM回归滚动训练验证模型
基于lgbm_classify改造，主要特点：
1. 使用LightGBM回归模型预测未来10根K线的平均收益率
2. seq_len=1，输入X降维到(batch_size, features)
3. 滚动训练验证，每次只训练前batch_size个，验证后面n个后，继续滚动训练
4. 支持实盘定期重新训练
"""

import numpy as np
import pandas as pd
import lightgbm as lgb
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import matplotlib
import pickle
import os
import time

from typing import Tuple, List, Optional, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# 导入trmV2的特征工程函数
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from trm.trmV2 import engineer_features_regression, rolling_normalize

# 导入字体配置工具
try:
    # 添加项目根目录到Python路径
    import sys
    import os
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    utils_path = os.path.join(project_root, 'utils')
    if utils_path not in sys.path:
        sys.path.insert(0, utils_path)

    # 导入并使用字体配置
    import font_config  # type: ignore
    font_config.setup_chinese_font()
except (ImportError, ModuleNotFoundError):
    # 如果导入失败，使用简单的字体设置
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', 'sans-serif']
    matplotlib.rcParams['axes.unicode_minus'] = False
    print("⚠️ 使用简单字体配置（无法导入font_config模块）")


class LightGBMStockRegressionModel:
    """
    LightGBM股票收益率预测回归模型，支持滚动训练验证
    """

    def __init__(self,
                 batch_size: int = 500,
                 validation_size: int = 100,
                 retrain_frequency: int = 50,
                 model_params: Optional[Dict[str, Any]] = None):
        """
        初始化模型

        Args:
            batch_size: 每次训练的样本数量
            validation_size: 每次验证的样本数量
            retrain_frequency: 实盘重新训练频率（预测多少次后重新训练）
            model_params: LightGBM模型参数
        """
        self.batch_size = batch_size
        self.validation_size = validation_size
        self.retrain_frequency = retrain_frequency
        self.prediction_count = 0

        # 专门针对大趋势预测的LightGBM参数
        self.default_params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 127,          # 大幅增加叶子数，提高对极值的敏感度
            'learning_rate': 0.05,      # 降低学习率，让模型更仔细学习
            'feature_fraction': 0.9,    # 保留更多特征信息
            'bagging_fraction': 0.8,    # 保留更多样本信息
            'bagging_freq': 5,
            'min_data_in_leaf': 5,      # 大幅减少叶子节点最小样本数，允许更细粒度分割
            'min_gain_to_split': 0.001, # 大幅降低分裂阈值，更容易分割极值
            'lambda_l1': 0.01,          # 大幅减少正则化，让模型更激进
            'lambda_l2': 0.01,
            'verbose': -1,
            'random_state': 42,
            'n_estimators': 300,        # 大幅增加树的数量
            'early_stopping_rounds': 30,
            'max_depth': 12,            # 增加树的深度
            'min_child_weight': 1       # 降低子节点权重要求
        }

        # 趋势分类阈值
        self.trend_thresholds = {
            'strong_down': -0.02,   # 强烈下跌：-2%以下
            'weak_down': -0.005,    # 轻微下跌：-0.5%到-2%
            'sideways': 0.005,      # 震荡：-0.5%到+0.5%
            'weak_up': 0.02,        # 轻微上涨：+0.5%到+2%
            'strong_up': float('inf') # 强烈上涨：+2%以上
        }

        # 更新参数
        if model_params:
            self.default_params.update(model_params)

        self.model = None
        self.feature_names = None
        self.training_history = []  # 训练历史记录
        self.validation_history = []  # 验证历史记录

    def create_trend_focused_weights(self, y: np.ndarray) -> np.ndarray:
        """
        创建专注于大趋势的样本权重策略，特别加强下跌预测能力

        权重分配策略：
        - 强烈下跌（≤-2%）：权重 × 15 🔥 (特别加强)
        - 强烈上涨（≥+2%）：权重 × 10
        - 中等下跌（-2%到-0.5%）：权重 × 5 (加强下跌敏感度)
        - 中等上涨（+0.5%到+2%）：权重 × 3
        - 震荡区间（±0.5%以内）：权重 × 0.05（几乎忽略）

        Args:
            y: 目标变量（收益率）

        Returns:
            样本权重数组
        """
        weights = np.ones(len(y))

        # 强烈下跌：权重×15 (特别加强下跌预测)
        strong_down_mask = y <= self.trend_thresholds['strong_down']
        weights[strong_down_mask] *= 15.0

        # 强烈上涨：权重×10
        strong_up_mask = y >= self.trend_thresholds['weak_up']
        weights[strong_up_mask] *= 10.0

        # 中等下跌：权重×5 (加强下跌敏感度)
        medium_down_mask = (y > self.trend_thresholds['strong_down']) & (y <= self.trend_thresholds['weak_down'])
        weights[medium_down_mask] *= 5.0

        # 中等上涨：权重×3
        medium_up_mask = (y > self.trend_thresholds['sideways']) & (y < self.trend_thresholds['weak_up'])
        weights[medium_up_mask] *= 3.0

        # 震荡区间：权重×0.05（几乎完全忽略）
        sideways_mask = (y > self.trend_thresholds['weak_down']) & (y <= self.trend_thresholds['sideways'])
        weights[sideways_mask] *= 0.05

        # 统计信息
        strong_down_count = strong_down_mask.sum()
        strong_up_count = strong_up_mask.sum()
        medium_down_count = medium_down_mask.sum()
        medium_up_count = medium_up_mask.sum()
        sideways_count = sideways_mask.sum()

        print(f"下跌加强权重统计:")
        print(f"  总样本数: {len(y)}")
        print(f"  强烈下跌样本: {strong_down_count} ({strong_down_count/len(y)*100:.1f}%) - 权重×15 🔥")
        print(f"  强烈上涨样本: {strong_up_count} ({strong_up_count/len(y)*100:.1f}%) - 权重×10")
        print(f"  中等下跌样本: {medium_down_count} ({medium_down_count/len(y)*100:.1f}%) - 权重×5")
        print(f"  中等上涨样本: {medium_up_count} ({medium_up_count/len(y)*100:.1f}%) - 权重×3")
        print(f"  震荡区间样本: {sideways_count} ({sideways_count/len(y)*100:.1f}%) - 权重×0.05")
        print(f"  下跌/上涨权重比: {15.0/10.0:.1f}:1 (偏向下跌预测)")

        return weights

    def classify_trend(self, returns: np.ndarray) -> np.ndarray:
        """
        将收益率分类为趋势类别

        Args:
            returns: 收益率数组

        Returns:
            趋势类别数组 (0=强烈下跌, 1=轻微下跌, 2=震荡, 3=轻微上涨, 4=强烈上涨)
        """
        trends = np.zeros(len(returns), dtype=int)

        trends[returns <= self.trend_thresholds['strong_down']] = 0  # 强烈下跌
        trends[(returns > self.trend_thresholds['strong_down']) &
               (returns <= self.trend_thresholds['weak_down'])] = 1   # 轻微下跌
        trends[(returns > self.trend_thresholds['weak_down']) &
               (returns <= self.trend_thresholds['sideways'])] = 2    # 震荡
        trends[(returns > self.trend_thresholds['sideways']) &
               (returns < self.trend_thresholds['weak_up'])] = 3      # 轻微上涨
        trends[returns >= self.trend_thresholds['weak_up']] = 4      # 强烈上涨

        return trends

    def calculate_trend_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算趋势预测准确性指标，特别关注下跌预测能力

        Args:
            y_true: 真实收益率
            y_pred: 预测收益率

        Returns:
            趋势准确性指标字典
        """
        true_trends = self.classify_trend(y_true)
        pred_trends = self.classify_trend(y_pred)

        # 总体趋势准确率
        trend_accuracy = np.mean(true_trends == pred_trends)

        # 大趋势准确率（只考虑强烈上涨和强烈下跌）
        strong_trend_mask = (true_trends == 0) | (true_trends == 4)
        if strong_trend_mask.sum() > 0:
            strong_trend_accuracy = np.mean(true_trends[strong_trend_mask] == pred_trends[strong_trend_mask])
        else:
            strong_trend_accuracy = 0.0

        # 专门的下跌预测准确率
        strong_down_mask = (true_trends == 0)  # 强烈下跌
        if strong_down_mask.sum() > 0:
            down_trend_accuracy = np.mean(true_trends[strong_down_mask] == pred_trends[strong_down_mask])
        else:
            down_trend_accuracy = 0.0

        # 专门的上涨预测准确率
        strong_up_mask = (true_trends == 4)  # 强烈上涨
        if strong_up_mask.sum() > 0:
            up_trend_accuracy = np.mean(true_trends[strong_up_mask] == pred_trends[strong_up_mask])
        else:
            up_trend_accuracy = 0.0

        # 方向准确率（只看涨跌方向，不管幅度）
        true_direction = np.sign(y_true)
        pred_direction = np.sign(y_pred)
        direction_accuracy = np.mean(true_direction == pred_direction)

        # 下跌方向准确率
        down_mask = (y_true < -0.005)  # 下跌超过0.5%
        if down_mask.sum() > 0:
            down_direction_accuracy = np.mean(true_direction[down_mask] == pred_direction[down_mask])
        else:
            down_direction_accuracy = 0.0

        # 上涨方向准确率
        up_mask = (y_true > 0.005)  # 上涨超过0.5%
        if up_mask.sum() > 0:
            up_direction_accuracy = np.mean(true_direction[up_mask] == pred_direction[up_mask])
        else:
            up_direction_accuracy = 0.0

        # 大幅波动的方向准确率
        large_move_mask = (np.abs(y_true) >= 0.01)  # 1%以上的波动
        if large_move_mask.sum() > 0:
            large_move_direction_accuracy = np.mean(
                true_direction[large_move_mask] == pred_direction[large_move_mask]
            )
        else:
            large_move_direction_accuracy = 0.0

        return {
            'trend_accuracy': trend_accuracy,
            'strong_trend_accuracy': strong_trend_accuracy,
            'down_trend_accuracy': down_trend_accuracy,
            'up_trend_accuracy': up_trend_accuracy,
            'direction_accuracy': direction_accuracy,
            'down_direction_accuracy': down_direction_accuracy,
            'up_direction_accuracy': up_direction_accuracy,
            'large_move_direction_accuracy': large_move_direction_accuracy,
            'strong_trend_samples': strong_trend_mask.sum(),
            'strong_down_samples': strong_down_mask.sum(),
            'strong_up_samples': strong_up_mask.sum(),
            'large_move_samples': large_move_mask.sum()
        }

    def prepare_data(self, csv_file: str, window: int = 200) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        准备训练数据，预测未来10根K线的平均收益率

        Args:
            csv_file: CSV文件路径
            window: 滚动标准化窗口大小

        Returns:
            X: 特征数据 [样本数, 特征数]
            y: 目标收益率 [样本数]
            dates: 日期列表
        """
        print(f"从 {csv_file} 加载数据...")

        # 1. 读取数据
        df = pd.read_csv(csv_file)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').reset_index(drop=True)
        print(f"原始数据形状: {df.shape}")

        # 2. 特征工程（包含目标变量）
        features_with_target = engineer_features_regression(df, future_periods=10)
        print(f"特征工程后形状: {features_with_target.shape}")

        # 3. 删除包含NaN的行
        features_with_target = features_with_target.dropna()
        print(f"删除NaN后数据形状: {features_with_target.shape}")

        # 6. 分离特征和目标
        X_features = features_with_target.iloc[:, :-1].values
        y_target = features_with_target['target'].values

        # 7. 滚动标准化（避免数据泄漏）
        print("开始滚动标准化...")
        X_normalized, valid_start = rolling_normalize(X_features, window=window)
        print(f"标准化完成，跳过了前 {valid_start} 个数据不足的样本")

        # 对应调整目标变量和日期
        y_target_valid = y_target[valid_start:]
        dates_valid = df['datetime'].iloc[valid_start:valid_start + len(y_target_valid)].dt.strftime('%Y-%m-%d %H:%M:%S').tolist()

        print(f"最终数据形状: X={X_normalized.shape}, y={y_target_valid.shape}")
        print(f"日期范围: {dates_valid[0]} 到 {dates_valid[-1]}")

        # 保存特征名称
        self.feature_names = [f'feature_{i}' for i in range(X_normalized.shape[1])]

        # 确保返回正确的numpy数组类型
        return X_normalized, np.array(y_target_valid), dates_valid

    def train_model(self, X_train: np.ndarray, y_train: np.ndarray,
                   X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        训练LightGBM回归模型

        Args:
            X_train: 训练特征
            y_train: 训练目标收益率
            X_val: 验证特征（可选）
            y_val: 验证目标收益率（可选）

        Returns:
            训练结果字典
        """
        print(f"训练改进回归模型，训练集大小: {X_train.shape}")
        print(f"训练目标统计: 均值={y_train.mean():.6f}, 标准差={y_train.std():.6f}")
        print(f"训练目标范围: [{y_train.min():.6f}, {y_train.max():.6f}]")

        # 创建趋势导向的样本权重
        sample_weights = self.create_trend_focused_weights(y_train)

        # 创建LightGBM数据集，加入样本权重
        if self.feature_names is not None:
            train_data = lgb.Dataset(X_train, label=y_train, weight=sample_weights, feature_name=self.feature_names)
        else:
            train_data = lgb.Dataset(X_train, label=y_train, weight=sample_weights)

        valid_sets = [train_data]
        valid_names = ['train']

        if X_val is not None and y_val is not None:
            val_weights = self.create_trend_focused_weights(y_val)
            if self.feature_names is not None:
                val_data = lgb.Dataset(X_val, label=y_val, weight=val_weights, feature_name=self.feature_names, reference=train_data)
            else:
                val_data = lgb.Dataset(X_val, label=y_val, weight=val_weights, reference=train_data)
            valid_sets.append(val_data)
            valid_names.append('valid')
            print(f"验证集大小: {X_val.shape}")
            print(f"验证目标统计: 均值={y_val.mean():.6f}, 标准差={y_val.std():.6f}")
            print(f"验证目标范围: [{y_val.min():.6f}, {y_val.max():.6f}]")

        # 训练模型
        start_time = time.time()
        self.model = lgb.train(
            self.default_params,
            train_data,
            valid_sets=valid_sets,
            valid_names=valid_names,
            callbacks=[lgb.log_evaluation(0)]  # 不打印训练日志
        )

        training_time = time.time() - start_time
        print(f"训练完成，耗时: {training_time:.2f}秒")

        # 计算训练集指标
        train_pred = self.model.predict(X_train)
        train_pred = np.array(train_pred)  # 确保是numpy数组
        train_mse = mean_squared_error(y_train, train_pred)
        train_mae = mean_absolute_error(y_train, train_pred)
        train_r2 = r2_score(y_train, train_pred)

        # 分析预测范围 - 关键诊断信息
        train_pred_std = train_pred.std()
        train_true_std = y_train.std()
        train_std_ratio = train_pred_std / train_true_std
        train_correlation = np.corrcoef(y_train, train_pred)[0, 1]

        # 计算趋势准确性指标
        train_trend_metrics = self.calculate_trend_accuracy(y_train, train_pred)

        print(f"训练集预测范围分析:")
        print(f"  预测范围: [{train_pred.min():.6f}, {train_pred.max():.6f}]")
        print(f"  预测标准差: {train_pred_std:.6f}")
        print(f"  真实标准差: {train_true_std:.6f}")
        print(f"  标准差比率: {train_std_ratio:.4f} ({train_std_ratio*100:.1f}%)")
        print(f"  预测相关性: {train_correlation:.4f}")

        print(f"训练集趋势准确性分析:")
        print(f"  总体趋势准确率: {train_trend_metrics['trend_accuracy']:.4f}")
        print(f"  大趋势准确率: {train_trend_metrics['strong_trend_accuracy']:.4f}")
        print(f"  📉 下跌预测准确率: {train_trend_metrics['down_trend_accuracy']:.4f}")
        print(f"  📈 上涨预测准确率: {train_trend_metrics['up_trend_accuracy']:.4f}")
        print(f"  方向准确率: {train_trend_metrics['direction_accuracy']:.4f}")
        print(f"  下跌方向准确率: {train_trend_metrics['down_direction_accuracy']:.4f}")
        print(f"  上涨方向准确率: {train_trend_metrics['up_direction_accuracy']:.4f}")
        print(f"  强下跌样本数: {train_trend_metrics['strong_down_samples']}")
        print(f"  强上涨样本数: {train_trend_metrics['strong_up_samples']}")

        result = {
            'training_time': training_time,
            'train_mse': train_mse,
            'train_mae': train_mae,
            'train_r2': train_r2,
            'train_rmse': np.sqrt(train_mse),
            'train_pred_std': train_pred_std,
            'train_true_std': train_true_std,
            'train_std_ratio': train_std_ratio,
            'train_correlation': train_correlation,
            # 趋势准确性指标
            'train_trend_accuracy': train_trend_metrics['trend_accuracy'],
            'train_strong_trend_accuracy': train_trend_metrics['strong_trend_accuracy'],
            'train_down_trend_accuracy': train_trend_metrics['down_trend_accuracy'],
            'train_up_trend_accuracy': train_trend_metrics['up_trend_accuracy'],
            'train_direction_accuracy': train_trend_metrics['direction_accuracy'],
            'train_down_direction_accuracy': train_trend_metrics['down_direction_accuracy'],
            'train_up_direction_accuracy': train_trend_metrics['up_direction_accuracy'],
            'train_large_move_direction_accuracy': train_trend_metrics['large_move_direction_accuracy']
        }

        # 如果有验证集，计算验证集指标
        if X_val is not None and y_val is not None:
            val_pred = self.model.predict(X_val)
            val_pred = np.array(val_pred)  # 确保是numpy数组
            val_mse = mean_squared_error(y_val, val_pred)
            val_mae = mean_absolute_error(y_val, val_pred)
            val_r2 = r2_score(y_val, val_pred)

            # 分析验证集预测范围
            val_pred_std = val_pred.std()
            val_true_std = y_val.std()
            val_std_ratio = val_pred_std / val_true_std
            val_correlation = np.corrcoef(y_val, val_pred)[0, 1]

            # 计算验证集趋势准确性指标
            val_trend_metrics = self.calculate_trend_accuracy(y_val, val_pred)

            print(f"验证集预测范围分析:")
            print(f"  预测范围: [{val_pred.min():.6f}, {val_pred.max():.6f}]")
            print(f"  预测标准差: {val_pred_std:.6f}")
            print(f"  标准差比率: {val_std_ratio:.4f} ({val_std_ratio*100:.1f}%)")
            print(f"  预测相关性: {val_correlation:.4f}")

            print(f"验证集趋势准确性分析:")
            print(f"  总体趋势准确率: {val_trend_metrics['trend_accuracy']:.4f}")
            print(f"  大趋势准确率: {val_trend_metrics['strong_trend_accuracy']:.4f}")
            print(f"  📉 下跌预测准确率: {val_trend_metrics['down_trend_accuracy']:.4f}")
            print(f"  📈 上涨预测准确率: {val_trend_metrics['up_trend_accuracy']:.4f}")
            print(f"  方向准确率: {val_trend_metrics['direction_accuracy']:.4f}")
            print(f"  下跌方向准确率: {val_trend_metrics['down_direction_accuracy']:.4f}")
            print(f"  上涨方向准确率: {val_trend_metrics['up_direction_accuracy']:.4f}")
            print(f"  强下跌样本数: {val_trend_metrics['strong_down_samples']}")
            print(f"  强上涨样本数: {val_trend_metrics['strong_up_samples']}")

            result.update({
                'val_mse': val_mse,
                'val_mae': val_mae,
                'val_r2': val_r2,
                'val_rmse': np.sqrt(val_mse),
                'val_pred_std': val_pred_std,
                'val_true_std': val_true_std,
                'val_std_ratio': val_std_ratio,
                'val_correlation': val_correlation,
                # 验证集趋势准确性指标
                'val_trend_accuracy': val_trend_metrics['trend_accuracy'],
                'val_strong_trend_accuracy': val_trend_metrics['strong_trend_accuracy'],
                'val_down_trend_accuracy': val_trend_metrics['down_trend_accuracy'],
                'val_up_trend_accuracy': val_trend_metrics['up_trend_accuracy'],
                'val_direction_accuracy': val_trend_metrics['direction_accuracy'],
                'val_down_direction_accuracy': val_trend_metrics['down_direction_accuracy'],
                'val_up_direction_accuracy': val_trend_metrics['up_direction_accuracy'],
                'val_large_move_direction_accuracy': val_trend_metrics['large_move_direction_accuracy']
            })

            print(f"\n=== 传统指标对比 ===")
            print(f"训练集 - RMSE: {result['train_rmse']:.6f}, R²: {train_r2:.4f}, 标准差比率: {train_std_ratio:.3f}")
            print(f"验证集 - RMSE: {result['val_rmse']:.6f}, R²: {val_r2:.4f}, 标准差比率: {val_std_ratio:.3f}")

            print(f"\n=== 🎯 趋势预测能力评估（核心指标）===")
            print(f"验证集大趋势准确率: {result['val_strong_trend_accuracy']:.1%}")
            print(f"📉 验证集下跌预测准确率: {result['val_down_trend_accuracy']:.1%}")
            print(f"📈 验证集上涨预测准确率: {result['val_up_trend_accuracy']:.1%}")
            print(f"验证集下跌方向准确率: {result['val_down_direction_accuracy']:.1%}")
            print(f"验证集上涨方向准确率: {result['val_up_direction_accuracy']:.1%}")

            # 下跌预测能力专门诊断
            down_trend_acc = result['val_down_trend_accuracy']
            up_trend_acc = result['val_up_trend_accuracy']
            down_direction_acc = result['val_down_direction_accuracy']

            print(f"\n=== 📊 下跌预测能力诊断 ===")
            if down_trend_acc >= 0.6 and down_direction_acc >= 0.65:
                print("🎉 优秀：模型对下跌预测很准确！")
            elif down_trend_acc >= 0.5 and down_direction_acc >= 0.55:
                print("✅ 良好：模型具备不错的下跌预测能力")
            elif down_trend_acc >= 0.4 and down_direction_acc >= 0.5:
                print("⚠️  一般：下跌预测能力有待提升")
            else:
                print("❌ 较差：下跌预测能力不足，需要进一步优化")

            print(f"\n=== 📊 上涨vs下跌预测对比 ===")
            if abs(down_trend_acc - up_trend_acc) <= 0.1:
                print("✅ 平衡：上涨和下跌预测能力相当")
            elif down_trend_acc > up_trend_acc:
                print(f"📉 偏向下跌：下跌预测比上涨预测准确 {(down_trend_acc - up_trend_acc)*100:.1f}%")
            else:
                print(f"📈 偏向上涨：上涨预测比下跌预测准确 {(up_trend_acc - down_trend_acc)*100:.1f}%")

        else:
            print(f"训练集 - RMSE: {result['train_rmse']:.6f}, R²: {train_r2:.4f}")
            print(f"训练集大趋势准确率: {result['train_strong_trend_accuracy']:.1%} | 方向准确率: {result['train_direction_accuracy']:.1%}")

        return result

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测收益率

        Args:
            X: 输入特征

        Returns:
            预测的收益率
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train_model方法")

        pred_returns = self.model.predict(X)
        return np.array(pred_returns)

    def rolling_train_validate(self, X: np.ndarray, y: np.ndarray, dates: List[str]) -> Dict[str, Any]:
        """
        滚动训练验证

        Args:
            X: 特征数据 [样本数, 特征数]
            y: 目标收益率 [样本数]
            dates: 日期列表

        Returns:
            验证结果字典
        """
        print(f"开始滚动训练验证，数据形状: X={X.shape}, y={y.shape}")
        print(f"批次大小: {self.batch_size}, 验证大小: {self.validation_size}")

        total_samples = len(X)
        current_pos = 0
        round_count = 0

        all_predictions = []
        all_true_values = []
        all_dates = []
        validation_results = []

        while current_pos + self.batch_size + self.validation_size <= total_samples:
            round_count += 1
            print(f"\n=== 第 {round_count} 轮训练验证 ===")

            # 训练数据：当前位置开始的batch_size个样本
            train_start = current_pos
            train_end = current_pos + self.batch_size
            X_train = X[train_start:train_end]
            y_train = y[train_start:train_end]

            # 验证数据：训练数据后面的validation_size个样本
            val_start = train_end
            val_end = train_end + self.validation_size
            X_val = X[val_start:val_end]
            y_val = y[val_start:val_end]
            val_dates = dates[val_start:val_end]

            print(f"训练范围: {train_start}-{train_end}, 验证范围: {val_start}-{val_end}")
            print(f"训练日期: {dates[train_start]} 到 {dates[train_end-1]}")
            print(f"验证日期: {dates[val_start]} 到 {dates[val_end-1]}")

            # 训练模型
            train_result = self.train_model(X_train, y_train, X_val, y_val)

            # 预测验证集
            pred_returns = self.predict(X_val)

            # 计算验证指标
            val_mse = mean_squared_error(y_val, pred_returns)
            val_mae = mean_absolute_error(y_val, pred_returns)
            val_r2 = r2_score(y_val, pred_returns)
            val_rmse = np.sqrt(val_mse)

            print(f"验证结果 - RMSE: {val_rmse:.6f}, MAE: {val_mae:.6f}, R²: {val_r2:.4f}")

            # 保存结果
            all_predictions.extend(pred_returns.tolist())
            all_true_values.extend(y_val.tolist())
            all_dates.extend(val_dates)

            # 记录本轮验证结果
            round_result = {
                'round': round_count,
                'train_start': train_start,
                'train_end': train_end,
                'val_start': val_start,
                'val_end': val_end,
                'train_date_start': dates[train_start],
                'train_date_end': dates[train_end-1],
                'val_date_start': dates[val_start],
                'val_date_end': dates[val_end-1],
                'val_mse': val_mse,
                'val_mae': val_mae,
                'val_r2': val_r2,
                'val_rmse': val_rmse,
                'train_mse': train_result['train_mse'],
                'train_mae': train_result['train_mae'],
                'train_r2': train_result['train_r2'],
                'train_rmse': train_result['train_rmse']
            }
            validation_results.append(round_result)

            # 记录历史
            self.training_history.append(train_result)
            self.validation_history.append({
                'round': round_count,
                'val_mse': val_mse,
                'val_mae': val_mae,
                'val_r2': val_r2,
                'val_rmse': val_rmse
            })

            # 移动到下一个位置
            current_pos += self.validation_size

        # 计算总体指标
        all_predictions = np.array(all_predictions)
        all_true_values = np.array(all_true_values)

        overall_mse = mean_squared_error(all_true_values, all_predictions)
        overall_mae = mean_absolute_error(all_true_values, all_predictions)
        overall_r2 = r2_score(all_true_values, all_predictions)
        overall_rmse = np.sqrt(overall_mse)

        print(f"\n=== 滚动验证完成 ===")
        print(f"总轮次: {round_count}")
        print(f"总验证样本数: {len(all_predictions)}")
        print(f"总体 RMSE: {overall_rmse:.6f}")
        print(f"总体 MAE: {overall_mae:.6f}")
        print(f"总体 R²: {overall_r2:.4f}")

        return {
            'total_rounds': round_count,
            'overall_mse': overall_mse,
            'overall_mae': overall_mae,
            'overall_r2': overall_r2,
            'overall_rmse': overall_rmse,
            'predictions': all_predictions.tolist(),
            'true_values': all_true_values.tolist(),
            'dates': all_dates,
            'validation_results': validation_results
        }

    def plot_results(self, results: Dict[str, Any], save_path: Optional[str] = None):
        """
        绘制回归结果

        Args:
            results: 滚动验证结果
            save_path: 保存路径（可选）
        """
        _, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 预测值 vs 真实值散点图
        predictions = np.array(results['predictions'])
        true_values = np.array(results['true_values'])

        # 绘制散点图
        axes[0, 0].scatter(true_values, predictions, alpha=0.6, s=20, color='blue', label='预测点')

        # 绘制完美预测线（红色虚线）- 如果预测完全准确，所有点都应该在这条线上
        min_val = min(true_values.min(), predictions.min())
        max_val = max(true_values.max(), predictions.max())
        axes[0, 0].plot([min_val, max_val], [min_val, max_val],
                       'r--', lw=2, label='完美预测线 (y=x)')

        axes[0, 0].set_xlabel('真实收益率', fontsize=12)
        axes[0, 0].set_ylabel('预测收益率', fontsize=12)
        axes[0, 0].set_title(f'预测准确性分析 (R² = {results["overall_r2"]:.4f})', fontsize=14)
        axes[0, 0].legend(fontsize=10)
        axes[0, 0].grid(True, alpha=0.3)

        # 添加说明文本
        axes[0, 0].text(0.05, 0.95,
                       '说明：点越接近红线，预测越准确',
                       transform=axes[0, 0].transAxes,
                       fontsize=10,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
                       verticalalignment='top')

        # 2. 残差图（预测误差分析）
        residuals = predictions - true_values
        axes[0, 1].scatter(predictions, residuals, alpha=0.6, s=20, color='green', label='预测误差')
        axes[0, 1].axhline(y=0, color='r', linestyle='--', lw=2, label='零误差线')
        axes[0, 1].set_xlabel('预测收益率', fontsize=12)
        axes[0, 1].set_ylabel('残差 (预测值 - 真实值)', fontsize=12)
        axes[0, 1].set_title('预测误差分布图', fontsize=14)
        axes[0, 1].legend(fontsize=10)
        axes[0, 1].grid(True, alpha=0.3)

        # 添加说明文本
        axes[0, 1].text(0.05, 0.95,
                       '说明：点越接近红线，误差越小\n正值=高估，负值=低估',
                       transform=axes[0, 1].transAxes,
                       fontsize=10,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8),
                       verticalalignment='top')

        # 3. 时间序列对比图 - 显示预测趋于均值的问题
        dates = pd.to_datetime(results['dates'])

        # 绘制真实收益率 - 使用更清晰的线条
        axes[1, 0].plot(dates, true_values, label='真实收益率', alpha=0.8, linewidth=1.5, color='#1f77b4', zorder=2)
        # 绘制预测收益率 - 使用更醒目的颜色和线条
        axes[1, 0].plot(dates, predictions, label='预测收益率', alpha=0.9, linewidth=2, color='#ff4444', zorder=3)

        # 添加零线
        axes[1, 0].axhline(y=0, color='gray', linestyle='--', alpha=0.5, linewidth=1, zorder=1)

        # 计算并显示关键统计信息
        pred_std = np.std(predictions)
        true_std = np.std(true_values)
        std_ratio = pred_std / true_std
        correlation = np.corrcoef(true_values, predictions)[0, 1]

        axes[1, 0].set_xlabel('时间', fontsize=12)
        axes[1, 0].set_ylabel('收益率', fontsize=12)
        axes[1, 0].set_title(f'预测效果时间序列对比\n标准差比率: {std_ratio:.3f} | 相关系数: {correlation:.3f}', fontsize=14)
        axes[1, 0].legend(fontsize=11, loc='upper right')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 不添加散点，保持时间序列图的清晰度

        # 添加详细说明文本
        info_text = f"""问题诊断:
• 预测标准差: {pred_std:.6f}
• 真实标准差: {true_std:.6f}
• 标准差比率: {std_ratio:.1%}
• 预测范围: [{np.min(predictions):.6f}, {np.max(predictions):.6f}]
• 真实范围: [{np.min(true_values):.6f}, {np.max(true_values):.6f}]"""

        if std_ratio < 0.3:
            problem_level = "❌ 严重: 预测过于保守"
        elif std_ratio < 0.5:
            problem_level = "⚠️ 中等: 预测范围偏小"
        else:
            problem_level = "✅ 良好: 预测范围合理"

        axes[1, 0].text(0.02, 0.02, f"{problem_level}\n{info_text}",
                       transform=axes[1, 0].transAxes,
                       fontsize=9,
                       bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.9),
                       verticalalignment='bottom')

        # 4. 各轮次验证指标变化
        validation_results = results['validation_results']
        if validation_results:  # 只有在有验证结果时才绘制
            rounds = [r['round'] for r in validation_results]
            val_rmse = [r['val_rmse'] for r in validation_results]
            val_r2 = [r['val_r2'] for r in validation_results]

            ax4_1 = axes[1, 1]
            ax4_2 = ax4_1.twinx()

            line1 = ax4_1.plot(rounds, val_rmse, 'b-o', label='RMSE (误差)', markersize=4, linewidth=2)
            line2 = ax4_2.plot(rounds, val_r2, 'r-s', label='R² (拟合度)', markersize=4, linewidth=2)

            ax4_1.set_xlabel('训练轮次', fontsize=12)
            ax4_1.set_ylabel('RMSE (越小越好)', color='b', fontsize=12)
            ax4_2.set_ylabel('R² (越大越好)', color='r', fontsize=12)
            ax4_1.set_title('模型性能变化趋势', fontsize=14)
            ax4_1.grid(True, alpha=0.3)

            # 合并图例
            lines = line1 + line2
            labels = [l.get_label() for l in lines]
            ax4_1.legend(lines, labels, loc='upper left', fontsize=10)

            # 添加说明文本
            ax4_1.text(0.02, 0.02,
                       '说明：蓝线下降、红线上升表示模型性能提升',
                       transform=ax4_1.transAxes,
                       fontsize=10,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
                       verticalalignment='bottom')
        else:
            # 如果没有验证结果，显示提示信息
            axes[1, 1].text(0.5, 0.5, '无验证数据\n(单次测试模式)',
                           transform=axes[1, 1].transAxes,
                           fontsize=14, ha='center', va='center',
                           bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
            axes[1, 1].set_title('验证指标', fontsize=14)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果图已保存到: {save_path}")

        plt.show()

    def get_feature_importance(self, top_n: int = 20) -> pd.DataFrame:
        """
        获取特征重要性

        Args:
            top_n: 返回前N个重要特征

        Returns:
            特征重要性DataFrame
        """
        if self.model is None:
            raise ValueError("模型尚未训练")

        importance = self.model.feature_importance(importance_type='gain')
        feature_names = self.feature_names if self.feature_names else [f'feature_{i}' for i in range(len(importance))]

        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)

        return importance_df.head(top_n)

    def save_model(self, filepath: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型尚未训练，无法保存")

        model_data = {
            'model': self.model,
            'feature_names': self.feature_names,
            'batch_size': self.batch_size,
            'validation_size': self.validation_size,
            'retrain_frequency': self.retrain_frequency,
            'default_params': self.default_params,
            'prediction_count': self.prediction_count,
            'training_history': self.training_history,
            'validation_history': self.validation_history
        }

        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        print(f"模型已保存到: {filepath}")

    def load_model(self, filepath: str):
        """加载模型"""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"模型文件不存在: {filepath}")

        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)

        self.model = model_data['model']
        self.feature_names = model_data['feature_names']
        self.batch_size = model_data['batch_size']
        self.validation_size = model_data['validation_size']
        self.retrain_frequency = model_data['retrain_frequency']
        self.default_params = model_data['default_params']
        self.prediction_count = model_data.get('prediction_count', 0)
        self.training_history = model_data.get('training_history', [])
        self.validation_history = model_data.get('validation_history', [])

        print(f"模型已从 {filepath} 加载")

    def predict_single(self, X: np.ndarray, retrain_data: Optional[Tuple[np.ndarray, np.ndarray]] = None) -> float:
        """
        单次预测（实盘使用）

        Args:
            X: 单个样本特征 [1, features] 或 [features]
            retrain_data: 重新训练数据 (X_retrain, y_retrain)，可选

        Returns:
            预测的收益率
        """
        if self.model is None:
            raise ValueError("模型尚未训练或加载")

        # 确保输入是2D数组
        if X.ndim == 1:
            X = X.reshape(1, -1)

        # 检查是否需要重新训练
        self.prediction_count += 1
        if (retrain_data is not None and
            self.prediction_count % self.retrain_frequency == 0):
            print(f"达到重训练频率 ({self.retrain_frequency})，开始重新训练...")
            X_retrain, y_retrain = retrain_data
            self.train_model(X_retrain, y_retrain)
            print("重新训练完成")

        # 预测
        pred_return = self.predict(X)
        return float(pred_return[0])


def create_time_series_comparison_plot(results: Dict[str, Any], save_path: str = './time_series_comparison.png'):
    """
    创建专门的时间序列对比图，突出显示预测趋于均值的问题

    Args:
        results: 包含预测结果的字典
        save_path: 保存路径
    """
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 提取数据
    predictions = np.array(results['predictions'])
    true_values = np.array(results['true_values'])
    dates = pd.to_datetime(results['dates'])

    # 计算关键统计量
    pred_std = predictions.std()
    true_std = true_values.std()
    std_ratio = pred_std / true_std
    correlation = np.corrcoef(true_values, predictions)[0, 1]

    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

    # 上图：时间序列对比 - 清晰的线条，无散点
    ax1.plot(dates, true_values, label='真实收益率', alpha=0.8, linewidth=1.5, color='#1f77b4', zorder=2)
    ax1.plot(dates, predictions, label='预测收益率', alpha=0.9, linewidth=2, color='#ff4444', zorder=3)
    ax1.axhline(y=0, color='gray', linestyle='--', alpha=0.5, linewidth=1, zorder=1)

    # 不添加散点，保持时间序列图清晰

    ax1.set_ylabel('收益率', fontsize=14)
    ax1.set_title(f'收益率预测时间序列对比\n标准差比率: {std_ratio:.3f} ({std_ratio*100:.1f}%) | 相关系数: {correlation:.3f}',
                 fontsize=16, pad=20)
    ax1.legend(fontsize=12, loc='upper right')
    ax1.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax1.tick_params(axis='x', rotation=45)

    # 添加问题诊断文本
    if std_ratio < 0.3:
        problem_text = "❌ 严重问题：预测过于保守"
        color = 'red'
    elif std_ratio < 0.5:
        problem_text = "⚠️ 中等问题：预测范围偏小"
        color = 'orange'
    else:
        problem_text = "✅ 良好：预测范围合理"
        color = 'green'

    info_text = f"""{problem_text}
预测标准差: {pred_std:.6f}
真实标准差: {true_std:.6f}
预测范围: [{predictions.min():.6f}, {predictions.max():.6f}]
真实范围: [{true_values.min():.6f}, {true_values.max():.6f}]"""

    ax1.text(0.02, 0.98, info_text, transform=ax1.transAxes, fontsize=11,
             bbox=dict(boxstyle="round,pad=0.5", facecolor=color, alpha=0.2),
             verticalalignment='top')

    # 下图：散点图对比
    ax2.scatter(true_values, predictions, alpha=0.6, s=30, color='blue', label='预测点')

    # 完美预测线
    min_val = min(true_values.min(), predictions.min())
    max_val = max(true_values.max(), predictions.max())
    ax2.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='完美预测线 (y=x)')

    ax2.set_xlabel('真实收益率', fontsize=14)
    ax2.set_ylabel('预测收益率', fontsize=14)
    ax2.set_title(f'预测准确性散点图 (R² = {correlation**2:.4f})', fontsize=14)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)

    # 添加散点图说明
    ax2.text(0.05, 0.95, '说明：点越接近红线，预测越准确\n点聚集在水平线表示预测趋于均值',
             transform=ax2.transAxes, fontsize=11,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
             verticalalignment='top')

    fig.tight_layout()
    fig.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"时间序列对比图已保存到: {save_path}")
    plt.show()


def run_lgbm_regression(csv_file: str, model_path: str,
                       batch_size: int = 500, validation_size: int = 100,
                       custom_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    运行LightGBM回归模型的滚动训练验证

    Args:
        csv_file: 数据文件路径
        model_path: 模型保存路径
        batch_size: 批次大小
        validation_size: 验证集大小
        custom_params: 自定义模型参数

    Returns:
        验证结果字典
    """
    # 创建模型
    model = LightGBMStockRegressionModel(
        batch_size=batch_size,
        validation_size=validation_size,
        model_params=custom_params
    )

    # 检查是否存在已保存的模型
    if os.path.exists(model_path):
        print(f"发现已保存的模型: {model_path}")
        model.load_model(model_path)

        # 准备数据进行测试
        print("准备数据进行测试...")
        X, y, dates = model.prepare_data(csv_file, window=200)

        # 使用最后一部分数据进行测试
        test_size = min(1000, len(X) // 4)  # 使用最后1000个样本或1/4的数据
        X_test = X[-test_size:]
        y_test = y[-test_size:]
        test_dates = dates[-test_size:]

        print(f"使用最后 {test_size} 个样本进行测试...")

        # 预测
        pred_returns = model.predict(X_test)

        # 计算指标
        mse = mean_squared_error(y_test, pred_returns)
        mae = mean_absolute_error(y_test, pred_returns)
        r2 = r2_score(y_test, pred_returns)
        rmse = np.sqrt(mse)

        print(f"测试结果 - RMSE: {rmse:.6f}, MAE: {mae:.6f}, R²: {r2:.4f}")

        return {
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'predictions': pred_returns,
            'true_values': y_test,
            'dates': test_dates
        }
    else:
        print("未找到已保存的模型，开始滚动训练验证...")
        # 进行滚动训练验证
        X, y, dates = model.prepare_data(csv_file, window=200)
        results = model.rolling_train_validate(X, y, dates)

        # 保存模型
        model.save_model(model_path)

        return results


def main():
    """主函数：演示专门针对大趋势预测的LightGBM模型"""
    print("=== 🎯 大趋势预测专用LightGBM模型 ===")
    print("目标：准确预测大涨大跌，忽略小幅震荡")

    # 配置参数
    csv_file = './big_data/AAPL_US_5min.csv'  # 数据文件
    model_path = './model_trend_focused_lgbm.pkl'  # 模型保存路径
    batch_size = 800  # 增加训练样本数，获得更多趋势样本
    validation_size = 200  # 增加验证样本数

    # 专门针对大趋势预测的LightGBM参数
    custom_params = {
        'num_leaves': 127,          # 大幅增加复杂度
        'learning_rate': 0.05,      # 降低学习率，更仔细学习
        'n_estimators': 300,        # 大幅增加树数量
        'lambda_l1': 0.01,          # 大幅减少正则化，让模型更激进
        'lambda_l2': 0.01,
        'min_data_in_leaf': 5,      # 大幅降低限制
        'max_depth': 12,            # 增加树深度
        'min_child_weight': 1,      # 降低子节点权重要求
        'random_state': 42
    }

    print(f"🔥 下跌预测加强配置：")
    print(f"  - 强烈下跌样本权重: ×15 (特别加强)")
    print(f"  - 强烈上涨样本权重: ×10")
    print(f"  - 中等下跌样本权重: ×5 (加强下跌敏感度)")
    print(f"  - 中等上涨样本权重: ×3")
    print(f"  - 震荡区间样本权重: ×0.05 (几乎忽略)")
    print(f"  - 下跌/上涨权重比: 1.5:1 (偏向下跌预测)")
    print(f"  - 强烈趋势阈值: ±2%")
    print(f"  - 震荡区间阈值: ±0.5%")

    try:
        # 运行模型
        results = run_lgbm_regression(
            csv_file=csv_file,
            model_path=model_path,
            batch_size=batch_size,
            validation_size=validation_size,
            custom_params=custom_params
        )

        # 创建模型实例用于后续操作
        model = LightGBMStockRegressionModel(
            batch_size=batch_size,
            validation_size=validation_size,
            model_params=custom_params
        )

        # 加载模型
        model.load_model(model_path)

        # 绘制结果
        if 'validation_results' in results:  # 滚动验证结果
            print("绘制滚动验证结果...")
            model.plot_results(results, save_path='./lgbm_regression_results.png')
            # 额外生成专门的时间序列对比图
            create_time_series_comparison_plot(results, save_path='./time_series_comparison_rolling.png')
        else:  # 测试结果
            print("绘制测试结果...")
            # 为测试结果创建简化的结果字典
            test_results = {
                'predictions': results['predictions'].tolist() if hasattr(results['predictions'], 'tolist') else results['predictions'],
                'true_values': results['true_values'].tolist() if hasattr(results['true_values'], 'tolist') else results['true_values'],
                'dates': results['dates'],
                'overall_r2': results['r2'],
                'validation_results': []  # 空的验证结果
            }
            model.plot_results(test_results, save_path='./lgbm_regression_test_results.png')
            # 生成专门的时间序列对比图
            create_time_series_comparison_plot(test_results, save_path='./time_series_comparison.png')

        # 显示特征重要性
        print("\n=== 特征重要性 (Top 10) ===")
        feature_imp = model.get_feature_importance(top_n=10)
        print(feature_imp)

        # 保存详细结果
        if 'validation_results' in results:
            results_df = pd.DataFrame(results['validation_results'])
            results_df.to_csv('./lgbm_regression_validation_results.csv', index=False)
            print("详细结果已保存到: ./lgbm_regression_validation_results.csv")

            print(f"\n=== 最终结果 ===")
            print(f"总体 RMSE: {results['overall_rmse']:.6f}")
            print(f"总体 MAE: {results['overall_mae']:.6f}")
            print(f"总体 R²: {results['overall_r2']:.4f}")
            print(f"总验证样本数: {len(results['predictions'])}")
            print(f"总训练轮次: {results['total_rounds']}")
        else:
            print(f"\n=== 测试结果 ===")
            print(f"RMSE: {results['rmse']:.6f}")
            print(f"MAE: {results['mae']:.6f}")
            print(f"R²: {results['r2']:.4f}")
            print(f"测试样本数: {len(results['predictions'])}")

    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()