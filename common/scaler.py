import numpy as np
import pandas as pd

class CustomScaler:
    def __init__(self):
        self.positive_scale = 0.19  # 正数归一化比例, np.log(1.2)
        self.negative_scale = 0.23  # 负数归一化比例, np.log(0.8)
        self.positive_scale_vol = 1.0  # 正数归一化比例, np.log10(10)
        self.negative_scale_vol = -1.0  # 负数归一化比例, np.log10(0.1)
        self._is_fitted = False  # 是否已经拟合

    def fit(self, df):
        """
        拟合数据，计算归一化参数（这里不需要额外计算，因为参数是固定的）
        """
        pass

    def transform(self, df):
        """
        对数据进行归一化
        """
        normalized_df = pd.DataFrame()
        for column in df.columns:
            if column == 'Return':  # 归一化到 [-1, 1]
                values = df[column].values
                normalized_dim = np.zeros_like(values)
                # 正数除以 0.19
                positive_mask = values > 0
                normalized_dim[positive_mask] = values[positive_mask] / self.positive_scale
                # 负数除以 0.23
                negative_mask = values < 0
                normalized_dim[negative_mask] = values[negative_mask] / self.negative_scale
                # 0 值保持不变
                normalized_df[column] = normalized_dim.ravel()
            elif column == 'VolChg':  # 归一化到 [-1, 1]
                values = df[column].values
                normalized_dim = np.zeros_like(values)
                # 正数
                positive_mask = values > 0
                normalized_dim[positive_mask] = np.minimum(values[positive_mask], 1.0)
                # 负数
                negative_mask = values < 0
                normalized_dim[negative_mask] = np.maximum(values[negative_mask], -1.0)
                # 0 值保持不变
                normalized_df[column] = normalized_dim.ravel()
            elif column == 'IBS':  # 范围是 [0, 1]，无需归一化
                normalized_df[column] = df[column].values
        return normalized_df

    def fit_transform(self, df):
        """
        拟合数据并归一化
        """
        self.fit(df)
        return self.transform(df)

    def inverse_transform(self, normalized_values):
        """
        对归一化后的数据进行反归一化
        """
        denormalized_values = np.zeros_like(normalized_values)
        # 对正数进行反标准化
        positive_mask = normalized_values > 0
        denormalized_values[positive_mask] = normalized_values[positive_mask] * 0.19
        # 对负数进行反标准化
        negative_mask = normalized_values < 0
        denormalized_values[negative_mask] = normalized_values[negative_mask] * 0.23
        return denormalized_values