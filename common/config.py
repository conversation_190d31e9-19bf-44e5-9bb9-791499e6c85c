class Config:
	def __init__(
		self, input_window=20, output_window=1, test_set_size=1000, val_set_pct=0.1, batch_size=128, lr=0.005, 
		model_path='model.pth', num_epochs=1, 
	):
		self.input_window = input_window  # 用来预测的历史步长
		self.output_window = output_window  # 预测的未来时间步长
		self.test_set_size = test_set_size  # 测试集大小
		self.val_set_pct = val_set_pct  # 验证集在训练集中的占比
		self.batch_size = batch_size  # 批大小
		self.learning_rate = lr  # 学习率
		self.model_path = model_path  # 模型保存路径
		self.num_epochs = num_epochs  # 训练轮数