import os, time

from common.config import Config
import torch.nn as nn
import torch
from torch.utils.data import DataLoader
import time

class ModelTrainer:
    def __init__(self, model: nn.Module, config: Config):
        self.model = model
        self.config = config
        
    def train(self, train_loader: DataLoader, val_loader: DataLoader):
        model = self.model
        criterion = torch.nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=self.config.learning_rate)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 
            mode='min',
            factor=0.5,
            patience=5
        )
        start_time = time.time()
        best_val_loss = float('inf')
        patience = 10
        trigger_times = 0
        model_path = self.config.model_path
        if os.path.exists(model_path):
            print('Loading model from {}'.format(model_path))
            model.load_state_dict(torch.load(model_path, weights_only=True))
        num_epochs = self.config.num_epochs
        for epoch in range(num_epochs):
            # 训练阶段
            model.train()
            train_loss = 0.0
            '''
            TODO:
            刚开始: weight of size [32, 3, 4], expected input[128, 19, 6] to have 3 channels, but got 19 channels instead
            Permute前: Given groups=1, weight of size [32, 19, 4], expected input[128, 3, 22] to have 19 channels, but got 3 channels instead
            Permute后: 
            '''
            for batch_x, batch_y in train_loader:
                # 前向传播, 这里batch_x是(batch_size, seq_len, input_dim), 要转为(batch_size, input_dim, seq_len)
                batch_x = batch_x.permute(0, 2, 1)
                print(batch_x.shape)
                y_train_pred = model(batch_x)  # 输出维度为(batch_size, mid_channels, seq_len)
                print('y_train_pred.shape = ', y_train_pred.shape)
                print('batch_y.shape = ', batch_y.shape)
                loss = criterion(y_train_pred, batch_y)
                # 反向传播和优化
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                train_loss += loss.item() * batch_x.size(0)
            # 计算平均训练损失
            train_loss = train_loss / len(train_dataset)
            # 验证阶段
            model.eval()
            val_loss = 0.0
            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    y_val_pred = model(batch_x)
                    loss = criterion(y_val_pred, batch_y)
                    val_loss += loss.item() * batch_x.size(0)
            # 计算平均验证损失
            val_loss = val_loss / len(val_dataset)
            # 学习率调整
            scheduler.step(val_loss)
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch: {epoch}, Train_Loss: {train_loss:.6f}, Val_Loss: {val_loss:.6f}, Lr: {current_lr}")
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                trigger_times = 0
                torch.save(model.state_dict(), model_path)
            else:
                trigger_times += 1
                if trigger_times >= patience:
                    print('Early Stoped')
                    break
        training_time = time.time()-start_time
        print("Training time: {}".format(training_time))
    
    def evaluate(self, test_loader: DataLoader):
        model = self.model
        model.load_state_dict(torch.load(model_path, weights_only=True))
        model.eval()
        # 收集所有测试预测结果
        y_test_pred = []
        y_test_true = []
        with torch.no_grad():
            for batch_x, batch_y in test_loader:
                batch_pred = model(batch_x)
                y_test_pred.append(batch_pred)
                y_test_true.append(batch_y)
        # 将结果转换为numpy数组
        y_test_pred = torch.cat(y_test_pred).cpu().numpy()
        y_test_true = torch.cat(y_test_true).cpu().numpy()
        return y_test_pred, y_test_true

