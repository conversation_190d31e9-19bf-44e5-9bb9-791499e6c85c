import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

class Visualizer:
    @classmethod
    def plot_results(cls, orig_prices: np.ndarray, pred_prices: np.ndarray):
        sns.set_style("darkgrid")
        
        fig = plt.figure()
        ax = sns.lineplot(x = range(len(orig_prices)), y = orig_prices, label="Actual Price", color='royalblue')
        ax = sns.lineplot(x = range(len(pred_prices)), y = pred_prices, label="Predict Price", color='tomato')
        ax.set_title('Stock price', size = 14, fontweight='bold')
        ax.set_xlabel("Days", size = 14)
        ax.set_ylabel("Price", size = 14)
        ax.set_xticklabels('', size=10)

        plt.savefig('training_results.png')