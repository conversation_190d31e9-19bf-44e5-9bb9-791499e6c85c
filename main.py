from dotenv import load_dotenv
import numpy as np
from models.xgb.xgb_model import XGBoostStockModel, engineer_features_X
from longport.openapi import Config, QuoteContext, TradeContext, PushQuote, SubType,\
	OrderType, OrderSide, TimeInForceType, AdjustType, OutsideRTH, CalcIndex, Period
from threading import Thread
from datetime import datetime
import time
import pandas as pd
import pytz
import exchange_calendars as ecals

# 初始化行情和交易ctx
config = Config.from_env()
trade_ctx = TradeContext(config)
query_ctx = QuoteContext(config)
symbol = "TQQQ.US"
seq_len = 60
feature_cols = [
	'open', 'high', 'low', 'close', 'volume', 'turnover',
	'price_range', 'price_position',
	'ma5', 'ma20', 'ma_ratio',
	'volume_ratio', 'volatility', 'rsi',
]
model_path = './model_xgb_balanced.pkl'


def is_us_trading_window():
	"""
	判断是否在美股交易窗口内：
	1. 首先检查是否是交易日
	2. 然后检查时间是否在 盘前1小时后 (5:00AM ET) 到 盘后收盘1小时前 (7:00PM ET)
	"""
	eastern = pytz.timezone("America/New_York")
	now = datetime.now(eastern)
	now_str = now.strftime("%Y-%m-%d")

	# 1. 检查是否是交易日
	nyse = ecals.get_calendar("XNYS")
	if not nyse.is_session(now_str):
		print(f"今天 {now_str} 不是美股交易日")
		return False

	# 2. 检查时间窗口：5:00AM - 7:00PM (19:00)
	current_time = now.hour + now.minute/60  # 转换为小时小数表示
	is_in_window = 5.0 <= current_time < 19.0

	if not is_in_window:
		print(f"当前美东时间 {now.strftime('%H:%M')}, 不在交易窗口内 (5:00-19:00)")

	return is_in_window


def get_fresh_1min_data(symbol: str, query_ctx: QuoteContext, count: int) -> pd.DataFrame:
	"""获取新鲜的1分钟K线数据"""
	while True:
		response = query_ctx.candlesticks(
			symbol=symbol,
			period=Period.Min_1,  # 1分钟周期
			count=count,
			adjust_type=AdjustType.ForwardAdjust,
		)

		if not response:
			time.sleep(1)
			continue

		latest_candle = response[-1]
		now = datetime.now()

		# 计算最新K线的时间差
		time_diff = (now - latest_candle.timestamp).total_seconds()

		# 判断数据新鲜度：1分钟K线应该在5秒内
		if time_diff > 5:  # 超过5秒认为数据不够新鲜
			print(f"1分钟K线数据不够新鲜，时间差: {time_diff:.1f}秒")
			time.sleep(1)
			continue

		# 转换为DataFrame
		data = []
		for candle in response:
			data.append({
				'datetime': candle.timestamp,
				'open': candle.open,
				'high': candle.high,
				'low': candle.low,
				'close': candle.close,
				'volume': candle.volume,
				'turnover': candle.turnover,
			})

		df = pd.DataFrame(data)
		df['datetime'] = pd.to_datetime(df['datetime'])
		df.set_index('datetime', inplace=True)

		# 调试信息
		print(f"获取1分钟K线完成，数据量: {len(df)}, 索引类型: {type(df.index)}")
		print(f"时间范围: {df.index.min()} 到 {df.index.max()}")

		return df


def combine_1min_to_5min(df_1min: pd.DataFrame) -> pd.DataFrame:
	"""将1分钟K线合成5分钟K线"""
	# 确保索引是datetime类型
	if not isinstance(df_1min.index, pd.DatetimeIndex):
		print("警告：输入DataFrame的索引不是DatetimeIndex类型")
		return pd.DataFrame()

	# 使用label='right'让时间戳是每个窗口的结束时间
	df_5min = df_1min.resample('5T', label='right').agg({
		'open': 'first',
		'high': 'max',
		'low': 'min',
		'close': 'last',
		'volume': 'sum',
		'turnover': 'sum'
	}).dropna()  # 时间戳是09:04, 09:09, 09:14...

	# 确保返回的DataFrame保持datetime索引
	print(f"合成5分钟K线完成，索引类型: {type(df_5min.index)}")
	print(f"时间范围: {df_5min.index.min()} 到 {df_5min.index.max()}")

	return df_5min


def get_5min_df(symbol: str, query_ctx: QuoteContext, count: int) -> pd.DataFrame:
	# 获取300个1分钟K线来合成60个5分钟K线
	df_1min = get_fresh_1min_data(symbol, query_ctx, count=5*count)
	# 合成5分钟K线
	df_5min = combine_1min_to_5min(df_1min)
	# 确保返回最新的count个5分钟K线
	if len(df_5min) > count:
		df_5min = df_5min.tail(count)
	return df_5min


def main():
	# 加载环境变量
	load_dotenv()
	print("使用XGBoost模型进行股票预测")

	# 设置序列长度
	seq_len = 60  # 使用60个5分钟K线作为输入序列

	# 加载XGBoost模型
	model = XGBoostStockModel()
	model.load(model_path)
	print("XGBoost模型加载完成")
	# 开一个线程来实时抓取行情数据
	while True:
		try:
			# 检查是否在交易时间窗口内
			if not is_us_trading_window():
				print("不在交易时间窗口内，等待1小时后重新检查...")
				time.sleep(3600)
				continue

			# 获取5分钟K线数据
			df = get_5min_df(symbol, query_ctx, count=seq_len)
			print(f"获取到 {len(df)} 条5分钟K线数据")

			# 特征工程
			features_df = engineer_features_X(df)
			print(f"特征工程完成，特征数量: {features_df.shape[1]}")

			# 选择模型需要的特征列
			X_features = features_df[feature_cols].dropna()
			print(f"选择特征后形状: {X_features.shape}")

			# 为XGBoost准备数据：将时间序列特征展平
			if len(X_features) >= seq_len:
				# 取最近seq_len个时间步的数据并展平
				X_flat = X_features.iloc[-seq_len:].values.flatten().reshape(1, -1)
				print(f"XGBoost输入形状: {X_flat.shape}")

				# 模型预测
				probs = model.predict_proba(X_flat)[0]  # [3] - 预测概率
				pred_class = int(model.predict(X_flat)[0])  # 预测类别
				pred_pct = probs[pred_class] * 100

				# 输出预测结果
				eastern = pytz.timezone("America/New_York")
				current_et = datetime.now(eastern).strftime('%H:%M ET')
				class_names = ['跌', '平', '涨']
				print(f"[{current_et}] 预测结果: {class_names[pred_class]}, 概率: {pred_pct:.1f}%")
				print(f"预测概率: 跌={probs[0]:.3f}, 平={probs[1]:.3f}, 涨={probs[2]:.3f}")
				print(f"最新价格: {df['close'].iloc[-1]:.3f}")
				print("-" * 50)
			else:
				print(f"数据不足，需要至少{seq_len}个时间步，当前只有{len(X_features)}个")
				print("-" * 50)

		except Exception as e:
			print(f"预测异常: {e}")
			time.sleep(30)

		time.sleep(280)  # 等待约5分钟


if __name__ == '__main__':
	main()