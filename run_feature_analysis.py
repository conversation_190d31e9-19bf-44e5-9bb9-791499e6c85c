"""
运行特征重要性分析的简化脚本
"""

import os
import sys
from feature_importance_analysis import FeatureImportanceAnalyzer

def main():
    """主函数"""
    print("🚀 股票特征重要性分析工具")
    print("=" * 50)
    
    # 数据文件路径 - 使用big_data目录中的文件
    data_files = [
        "big_data/TQQQ_US_5min.csv",
        "big_data/NVDA_US_5min.csv",
        "big_data/AAPL_US_5min.csv",
        "big_data/AMZN_US_5min.csv",
        # 添加更多数据文件...
    ]
    
    # 查找存在的数据文件
    available_files = []
    for file_path in data_files:
        if os.path.exists(file_path):
            available_files.append(file_path)
            
    if not available_files:
        print("❌ 未找到数据文件，请检查以下路径:")
        for file_path in data_files:
            print(f"   - {file_path}")
        print("\n请确保数据文件存在，或修改 run_feature_analysis.py 中的文件路径")
        return
        
    print(f"📁 找到 {len(available_files)} 个数据文件:")
    for i, file_path in enumerate(available_files, 1):
        print(f"   {i}. {file_path}")
        
    # 选择要分析的文件
    if len(available_files) == 1:
        selected_file = available_files[0]
        print(f"\n🎯 自动选择: {selected_file}")
    else:
        print(f"\n请选择要分析的文件 (1-{len(available_files)}):")
        try:
            choice = int(input("输入序号: ")) - 1
            if 0 <= choice < len(available_files):
                selected_file = available_files[choice]
            else:
                print("❌ 无效选择，使用第一个文件")
                selected_file = available_files[0]
        except ValueError:
            print("❌ 输入无效，使用第一个文件")
            selected_file = available_files[0]
            
    print(f"🔍 开始分析文件: {selected_file}")
    print("=" * 50)
    
    try:
        # 创建分析器并运行分析
        analyzer = FeatureImportanceAnalyzer(selected_file)
        comprehensive_ranking = analyzer.run_full_analysis()
        
        print("\n" + "=" * 50)
        print("✅ 分析完成！")
        print("\n🏆 最重要的15个特征:")
        print("-" * 40)
        
        top_features = comprehensive_ranking.head(15)
        for i, (_, row) in enumerate(top_features.iterrows(), 1):
            feature = row['feature']
            avg_rank = row['avg_rank']
            print(f"{i:2d}. {feature:<25} (排名: {avg_rank:.2f})")
            
        print("\n📊 输出文件:")
        print("   - feature_importance_analysis.png  (可视化图表)")
        print("   - feature_importance_*.csv        (详细数据)")
        
        print("\n💡 使用建议:")
        print("   1. 查看生成的PNG图表了解各方法的特征重要性")
        print("   2. 重点关注综合排名前10的特征")
        print("   3. 考虑特征组的整体表现")
        print("   4. 结合业务逻辑验证特征的合理性")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        print("请检查数据文件格式是否正确")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
